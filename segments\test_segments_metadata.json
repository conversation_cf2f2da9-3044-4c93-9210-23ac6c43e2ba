[{"title": "第一章 引言", "text": "第一章 引言\n\n本文档介绍了文本预处理的基本概念和方法。文本预处理是自然语言处理的重要步骤，包括文本清洗、分词、向量化等过程。", "index": 1, "source_name": "test", "file_path": "segments\\test_seg1.txt"}, {"title": "第二章 文本解析", "text": "第二章 文本解析\n\n文本解析是将不同格式的文档转换为纯文本的过程。支持的格式包括PDF、DOCX、TXT等。", "index": 2, "source_name": "test", "file_path": "segments\\test_seg2.txt"}, {"title": "2.1 PDF解析", "text": "2.1 PDF解析\nPDF文档解析使用pdfplumber库，可以提取文本内容和页面信息。", "index": 3, "source_name": "test", "file_path": "segments\\test_seg3.txt"}, {"title": "2.2 DOCX解析", "text": "2.2 DOCX解析\nDOCX文档解析使用python-docx库，可以保留文档结构信息。", "index": 4, "source_name": "test", "file_path": "segments\\test_seg4.txt"}, {"title": "第三章 文本切分", "text": "第三章 文本切分\n\n文本切分包括章节切分和细粒度切分两个层次。章节切分保持语义完整性，细粒度切分便于检索。", "index": 5, "source_name": "test", "file_path": "segments\\test_seg5.txt"}, {"title": "第四章 向量化", "text": "第四章 向量化\n\n向量化是将文本转换为数值向量的过程，支持本地模型和云端API两种方式。", "index": 6, "source_name": "test", "file_path": "segments\\test_seg6.txt"}, {"title": "第五章 检索", "text": "第五章 检索\n\n基于向量相似度进行文本检索，支持FAISS和基础相似度计算两种方法。", "index": 7, "source_name": "test", "file_path": "segments\\test_seg7.txt"}]