{"API_RETRY_DELAY": 1, "API_RETRY_TIMES": 3, "API_TIMEOUT": 60, "CHAPTER_PATTERNS": ["^第[一二三四五六七八九十\\d]+章.*", "^Chapter\\s+\\d+.*", "^第[一二三四五六七八九十\\d]+节.*", "^Section\\s+\\d+.*", "^[一二三四五六七八九十\\d]+\\..*", "^##?\\s+.*", "^\\d+\\.\\d+.*"], "CHUNKS_DIR": "chunks", "CUSTOM_API_KEY": null, "CUSTOM_BASE_URL": null, "CUSTOM_MODEL": null, "DASHSCOPE_API_KEY": null, "DASHSCOPE_BASE_URL": "https://dashscope.aliyuncs.com/api/v1", "DEFAULT_CHUNK_SIZE": 1500, "DEFAULT_EMBEDDING_MODEL": "Qwen/Qwen3-Embedding-0.6B", "DEFAULT_ENCODING": "utf-8", "DEFAULT_OVERLAP": 100, "DEFAULT_SUMMARY_PROMPT": "请为以下文本生成一个简洁的摘要，要求：\n1. 概括主要内容和核心观点\n2. 保持逻辑清晰，语言简洁\n3. 长度控制在200-300字\n4. 保留重要的关键信息\n\n文本内容：\n{text}\n\n摘要：", "DEFAULT_TOP_K": 5, "EMBEDDINGS_DIR": "embeddings", "EMBEDDING_BATCH_SIZE": 32, "EMBEDDING_DIMENSION": 1024, "FALLBACK_ENCODINGS": ["utf-8", "gbk", "gb2312", "utf-8-sig"], "LOG_FILE": "logs/preprocessor.log", "LOG_FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "LOG_LEVEL": "INFO", "MAX_CHUNK_SIZE": 4000, "MAX_WORKERS": 4, "MEMORY_LIMIT_GB": 8, "MIN_CHUNK_SIZE": 100, "OPENAI_API_KEY": "sk-7251d850d7274b2f90adafcec54ce47f", "OPENAI_BASE_URL": "https://api.deepseek.com/v1", "OPENAI_MODEL": "deepseek-reasoner", "PARSED_TEXT_DIR": "parsed_text", "RAW_FILES_DIR": "raw_files", "RESULTS_DIR": "processed_results", "SEGMENTS_DIR": "segments", "SIMILARITY_THRESHOLD": 0.5, "SUMMARIES_DIR": "summaries", "SUMMARY_MAX_LENGTH": 300, "SUMMARY_MIN_LENGTH": 50, "SUMMARY_TEMPERATURE": 0.3, "SUMMARY_TOP_P": 0.9, "SUPPORTED_EXTENSIONS": [".pdf", ".docx", ".doc", ".txt"], "TOOLS_DIR": "tools", "USE_FAISS": true, "USE_LOCAL_EMBEDDING": true}