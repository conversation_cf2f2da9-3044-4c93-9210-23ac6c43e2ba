"""
文本预处理模块主流程
整合所有工具类，实现完整的文本预处理流程，从文件解析到向量化存储
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
import time

# 导入工具类
from tools.parser import Parser
from tools.splitter import Splitter
from tools.summarizer import Summarizer
from tools.embedding_tool import EmbeddingTool
from tools.retriever import Retriever


class TextPreprocessor:
    """
    文本预处理主类，整合所有工具类，实现完整的预处理流程
    """
    
    def __init__(self, 
                 use_local_embedding: bool = True,
                 embedding_model: str = "Qwen3-Embedding-0.6B",
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None):
        """
        初始化文本预处理器
        
        Args:
            use_local_embedding: 是否使用本地嵌入模型
            embedding_model: 嵌入模型名称
            api_key: API密钥
            base_url: API基础URL
        """
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化工具类
        self.parser = Parser()
        self.splitter = Splitter()
        self.summarizer = Summarizer(api_key=api_key, base_url=base_url)
        self.embedding_tool = EmbeddingTool(
            use_local=use_local_embedding,
            model_name=embedding_model,
            api_key=api_key,
            base_url=base_url
        )
        self.retriever = Retriever()
        
        self.logger.info("文本预处理器初始化完成")
    
    def process_file(self, file_path: str, 
                    generate_summary: bool = True,
                    generate_embeddings: bool = True,
                    chunk_size: int = 1000,
                    overlap: int = 100) -> Dict[str, Any]:
        """
        处理单个文件的完整流程
        
        Args:
            file_path: 文件路径
            generate_summary: 是否生成摘要
            generate_embeddings: 是否生成向量
            chunk_size: 细分块大小
            overlap: 重叠大小
            
        Returns:
            处理结果字典
        """
        start_time = time.time()
        file_path = Path(file_path)
        source_name = file_path.stem
        
        self.logger.info(f"开始处理文件: {file_path.name}")
        
        try:
            # 1. 文件解析
            self.logger.info("步骤1: 文件解析")
            parse_result = self.parser.parse(file_path)
            text = parse_result['text']
            metadata = parse_result['metadata']
            
            # 2. 章节切分
            self.logger.info("步骤2: 章节切分")
            headings = metadata.get('headings', None)
            segments = self.splitter.split_to_chapters(
                text, source_name, use_headings=headings
            )
            
            # 3. 生成摘要（可选）
            if generate_summary:
                self.logger.info("步骤3: 生成摘要")
                segments = self.summarizer.summarize_segments(segments, source_name)
                
                # 生成整体摘要
                summaries = [seg.get('summary', '') for seg in segments if seg.get('summary')]
                if summaries:
                    overall_summary = self.summarizer.create_overall_summary(summaries, source_name)
                else:
                    overall_summary = None
            else:
                overall_summary = None
            
            # 4. 细粒度切分
            self.logger.info("步骤4: 细粒度切分")
            all_chunks = []
            for i, segment in enumerate(segments):
                chunks = self.splitter.split_to_chunks(
                    segment['text'], source_name, i + 1, chunk_size, overlap
                )
                all_chunks.extend(chunks)
            
            # 5. 生成向量（可选）
            if generate_embeddings:
                self.logger.info("步骤5: 生成向量")
                
                # 为章节生成向量
                segments_with_embeddings = self.embedding_tool.embed_segments(
                    segments, source_name, use_summary=generate_summary
                )
                
                # 为细分块生成向量
                chunk_texts = [chunk['text'] for chunk in all_chunks]
                chunk_embeddings = self.embedding_tool.embed(chunk_texts)
                
                # 更新块信息
                for chunk, embedding in zip(all_chunks, chunk_embeddings):
                    chunk['embedding'] = embedding.tolist()
                    chunk['embedding_dim'] = len(embedding)
                
                # 保存块向量
                chunk_metadata = [{'index': i, 'source_name': source_name, 'type': 'chunk'} 
                                for i in range(len(all_chunks))]
                self.embedding_tool.save_embeddings(
                    chunk_embeddings, chunk_metadata, f"{source_name}_chunks"
                )
                
                # 添加到检索器
                self.retriever.add_vectors(chunk_embeddings, chunk_metadata)
                
            else:
                segments_with_embeddings = segments
            
            # 6. 保存处理结果
            result = {
                'source_file': str(file_path),
                'source_name': source_name,
                'parse_metadata': metadata,
                'segments': segments_with_embeddings,
                'chunks': all_chunks,
                'overall_summary': overall_summary,
                'processing_time': time.time() - start_time,
                'settings': {
                    'generate_summary': generate_summary,
                    'generate_embeddings': generate_embeddings,
                    'chunk_size': chunk_size,
                    'overlap': overlap
                }
            }
            
            # 保存完整结果
            result_file = Path("processed_results") / f"{source_name}_result.json"
            result_file.parent.mkdir(exist_ok=True)
            
            # 创建可序列化的结果（移除numpy数组）
            serializable_result = self._make_serializable(result)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"文件处理完成: {file_path.name}, 耗时: {result['processing_time']:.2f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"文件处理失败: {file_path.name}, 错误: {str(e)}")
            raise
    
    def process_directory(self, directory_path: str, **kwargs) -> List[Dict[str, Any]]:
        """
        批量处理目录中的文件
        
        Args:
            directory_path: 目录路径
            **kwargs: 传递给process_file的参数
            
        Returns:
            处理结果列表
        """
        directory = Path(directory_path)
        if not directory.exists():
            raise FileNotFoundError(f"目录不存在: {directory}")
        
        # 支持的文件扩展名
        supported_extensions = {'.pdf', '.docx', '.doc', '.txt'}
        
        # 查找支持的文件
        files = []
        for ext in supported_extensions:
            files.extend(directory.glob(f"*{ext}"))
        
        if not files:
            self.logger.warning(f"目录中没有找到支持的文件: {directory}")
            return []
        
        self.logger.info(f"找到{len(files)}个文件待处理")
        
        results = []
        for file_path in files:
            try:
                result = self.process_file(file_path, **kwargs)
                results.append(result)
            except Exception as e:
                self.logger.error(f"处理文件失败: {file_path.name}, 跳过")
                continue
        
        # 保存批量处理结果
        batch_result_file = Path("processed_results") / "batch_results.json"
        batch_result_file.parent.mkdir(exist_ok=True)
        
        serializable_results = [self._make_serializable(r) for r in results]
        with open(batch_result_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"批量处理完成，成功处理{len(results)}个文件")
        return results
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相关内容
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        try:
            results = self.retriever.search_text(query, self.embedding_tool, top_k)
            self.logger.info(f"搜索完成: '{query}', 返回{len(results)}个结果")
            return results
        except Exception as e:
            self.logger.error(f"搜索失败: {str(e)}")
            raise
    
    def save_retriever_index(self, filename: str = "main_index") -> str:
        """
        保存检索器索引
        
        Args:
            filename: 索引文件名
            
        Returns:
            保存路径
        """
        return self.retriever.save_index(filename)
    
    def load_retriever_index(self, filename: str = "main_index") -> None:
        """
        加载检索器索引
        
        Args:
            filename: 索引文件名
        """
        self.retriever.load_index(filename)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'retriever_stats': self.retriever.get_stats(),
            'directories': {
                'parsed_text': len(list(Path("parsed_text").glob("*.txt"))),
                'segments': len(list(Path("segments").glob("*.txt"))),
                'summaries': len(list(Path("summaries").glob("*.txt"))),
                'chunks': len(list(Path("chunks").glob("*.txt"))),
                'embeddings': len(list(Path("embeddings").glob("*.npz")))
            }
        }
    
    def _make_serializable(self, obj):
        """
        将对象转换为可序列化的格式
        
        Args:
            obj: 要转换的对象
            
        Returns:
            可序列化的对象
        """
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'tolist'):  # numpy数组
            return obj.tolist()
        elif isinstance(obj, Path):
            return str(obj)
        else:
            return obj


if __name__ == "__main__":
    # 示例用法
    preprocessor = TextPreprocessor()
    
    print("文本预处理器已创建")
    print("使用方法:")
    print("1. 处理单个文件: preprocessor.process_file('文件路径')")
    print("2. 批量处理: preprocessor.process_directory('目录路径')")
    print("3. 搜索: preprocessor.search('查询文本')")
    print("4. 保存索引: preprocessor.save_retriever_index()")
    print("5. 加载索引: preprocessor.load_retriever_index()")
    print("6. 获取统计: preprocessor.get_stats()")
