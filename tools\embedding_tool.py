"""
向量化工具类
支持本地Qwen3-Embedding-4B模型和阿里云API两种方式
"""

import logging
import os

import numpy as np
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import time

try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    SentenceTransformer = None

try:
    import requests
except ImportError:
    requests = None

try:
    import openai
    from openai import OpenAI
except ImportError:
    openai = None
    OpenAI = None


class EmbeddingTool:
    """
    向量化工具类，封装向量化方法，支持参数选择本地模型或云API
    """

    def __init__(self, output_dir: str = "embeddings", 
                 use_local: bool = True,
                 model_name: str = "Qwen/Qwen3-Embedding-0.6B",
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None):
        """
        初始化向量化工具
        
        Args:
            output_dir: 向量文件保存目录
            use_local: 是否使用本地模型
            model_name: 模型名称
            api_key: API密钥（云服务使用）
            base_url: API基础URL（云服务使用）
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.use_local = use_local
        self.model_name = model_name
        self.api_key = api_key
        self.base_url = base_url
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化模型或API
        self.model = None
        self.openai_client = None
        self._init_embedding_service()
    
    def _init_embedding_service(self):
        """初始化向量化服务"""
        if self.use_local:
            self._init_local_model()
        else:
            self._init_api_service()

    def _init_local_model(self):
        """初始化本地模型"""
        if SentenceTransformer is None:
            self.logger.error("sentence-transformers库未安装，无法使用本地模型")
            self.logger.info("请安装: pip install sentence-transformers")
            return

        try:
            # 项目根目录（假设在 preprocess_module 这里是根）
            BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
            model_path = os.path.join(BASE_DIR, "Qwen3-Embedding-0.6B")
            self.logger.info(f"正在加载本地模型: {self.model_name}")
            self.model = SentenceTransformer(model_path)
            self.logger.info("本地模型加载成功")
        except Exception as e:
            self.logger.error(f"本地模型加载失败: {str(e)}")
            self.logger.info("将尝试使用API服务")
            self.use_local = False
            self._init_api_service()
    
    def _init_api_service(self):
        """初始化API服务"""
        if not self.api_key:
            self.logger.warning("未设置API密钥，某些功能可能无法使用")

        # 初始化OpenAI客户端（如果需要）
        if OpenAI is not None and self.api_key:
            try:
                self.openai_client = OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url
                )
                self.logger.info("OpenAI嵌入客户端初始化成功")
            except Exception as e:
                self.logger.error(f"OpenAI嵌入客户端初始化失败: {str(e)}")
                self.openai_client = None

        if self.base_url:
            self.logger.info(f"使用自定义API服务: {self.base_url}")
        else:
            self.logger.info("使用默认API服务")

    def embed(self, text: Union[str, List[str]],
              batch_size: int = 32) -> Union[np.ndarray, List[np.ndarray]]:
        """
        生成文本向量

        Args:
            text: 单个文本或文本列表
            batch_size: 批处理大小

        Returns:
            向量或向量列表
        """
        if isinstance(text, str):
            return self._embed_single(text)
        else:
            return self._embed_batch(text, batch_size)
    
    def _embed_single(self, text: str) -> np.ndarray:
        """
        生成单个文本的向量
        
        Args:
            text: 文本内容
            
        Returns:
            文本向量
        """
        if not text.strip():
            raise ValueError("输入文本不能为空")
        
        try:
            if self.use_local and self.model:
                embedding = self.model.encode(text, convert_to_numpy=True)
            else:
                embedding = self._embed_via_api(text)
            
            return embedding
            
        except Exception as e:
            self.logger.error(f"向量化失败: {str(e)}")
            raise
    
    def _embed_batch(self, texts: List[str], batch_size: int) -> List[np.ndarray]:
        """
        批量生成文本向量
        
        Args:
            texts: 文本列表
            batch_size: 批处理大小
            
        Returns:
            向量列表
        """
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            try:
                if self.use_local and self.model:
                    batch_embeddings = self.model.encode(batch, convert_to_numpy=True)
                    embeddings.extend(batch_embeddings)
                else:
                    for text in batch:
                        embedding = self._embed_via_api(text)
                        embeddings.append(embedding)
                        time.sleep(0.1)  # 避免API限制
                
                self.logger.info(f"批次 {i//batch_size + 1} 向量化完成")
                
            except Exception as e:
                self.logger.error(f"批次 {i//batch_size + 1} 向量化失败: {str(e)}")
                raise
        
        return embeddings
    
    def _embed_via_api(self, text: str) -> np.ndarray:
        """
        通过API生成向量
        
        Args:
            text: 文本内容
            
        Returns:
            文本向量
        """
        if self.base_url:
            return self._embed_custom_api(text)
        else:
            return self._embed_openai_api(text)
    
    def _embed_custom_api(self, text: str) -> np.ndarray:
        """
        使用自定义API生成向量
        
        Args:
            text: 文本内容
            
        Returns:
            文本向量
        """
        if requests is None:
            raise ImportError("需要安装requests库: pip install requests")
        
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        data = {
            "model": self.model_name,
            "input": text
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            embedding = np.array(result["data"][0]["embedding"])
            return embedding
            
        except Exception as e:
            self.logger.error(f"自定义API调用失败: {str(e)}")
            raise
    
    def _embed_openai_api(self, text: str) -> np.ndarray:
        """
        使用OpenAI API生成向量

        Args:
            text: 文本内容

        Returns:
            文本向量
        """
        if self.openai_client is None:
            raise ImportError("OpenAI嵌入客户端未初始化，请检查API密钥和网络连接")

        try:
            # 使用新的OpenAI 1.x API调用方式
            response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input=text
            )

            embedding = np.array(response.data[0].embedding)
            return embedding

        except Exception as e:
            self.logger.error(f"OpenAI API调用失败: {str(e)}")
            raise
    
    def save_embeddings(self, embeddings: List[np.ndarray],
                       metadata: List[Dict[str, Any]],
                       filename: str) -> str:
        """
        保存向量到文件

        Args:
            embeddings: 向量列表
            metadata: 元数据列表
            filename: 文件名

        Returns:
            保存的文件路径
        """
        output_file = self.output_dir / f"{filename}.npz"

        # 转换为numpy数组
        embeddings_array = np.array(embeddings)

        # 保存向量
        np.savez_compressed(output_file, embeddings=embeddings_array)

        # 保存元数据
        metadata_file = self.output_dir / f"{filename}_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        self.logger.info(f"向量保存完成: {output_file}")
        return str(output_file)
    
    def load_embeddings(self, filename: str) -> tuple:
        """
        从文件加载向量
        
        Args:
            filename: 文件名（不含扩展名）
            
        Returns:
            (向量数组, 元数据列表)
        """
        # 加载向量
        vector_file = self.output_dir / f"{filename}.npz"
        if not vector_file.exists():
            raise FileNotFoundError(f"向量文件不存在: {vector_file}")
        
        data = np.load(vector_file)
        embeddings = data['embeddings']
        
        # 加载元数据
        metadata_file = self.output_dir / f"{filename}_metadata.json"
        metadata = []
        if metadata_file.exists():
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
        
        self.logger.info(f"向量加载完成: {vector_file}")
        return embeddings, metadata
    
    def embed_segments(self, segments: List[Dict[str, Any]], 
                      source_name: str, use_summary: bool = False) -> List[Dict[str, Any]]:
        """
        为章节生成向量
        
        Args:
            segments: 章节列表
            source_name: 源文件名
            use_summary: 是否使用摘要生成向量
            
        Returns:
            包含向量的章节列表
        """
        texts = []
        for segment in segments:
            if use_summary and 'summary' in segment:
                texts.append(segment['summary'])
            else:
                texts.append(segment['text'])
        
        # 生成向量
        embeddings = self.embed(texts)
        
        # 更新章节信息
        embedded_segments = []
        for i, (segment, embedding) in enumerate(zip(segments, embeddings)):
            segment_with_embedding = segment.copy()
            segment_with_embedding['embedding'] = embedding.tolist()
            segment_with_embedding['embedding_dim'] = len(embedding)
            embedded_segments.append(segment_with_embedding)
        
        # 保存向量
        metadata = [{'index': i, 'source_name': source_name, 'use_summary': use_summary} 
                   for i in range(len(segments))]
        
        filename = f"{source_name}_segments"
        if use_summary:
            filename += "_summary"
        
        self.save_embeddings(embeddings, metadata, filename)
        
        self.logger.info(f"章节向量化完成: {source_name}, 共{len(embeddings)}个向量")
        return embedded_segments


if __name__ == "__main__":
    # 测试代码
    embedding_tool = EmbeddingTool()
    
    # 示例用法
    print("EmbeddingTool类已创建")
    print("使用方法:")
    print("1. 单个向量: embedding_tool.embed('文本')")
    print("2. 批量向量: embedding_tool.embed(['文本1', '文本2'])")
    print("3. 章节向量: embedding_tool.embed_segments(segments, source_name)")
