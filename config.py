"""
配置文件
包含文本预处理模块的各种配置参数，使用JSON文件存储配置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any


class Config:
    """
    配置类，包含所有配置参数，支持从JSON文件加载和保存配置
    """
    
    # 项目根目录
    PROJECT_ROOT = Path(__file__).parent
    
    # 配置文件路径
    CONFIG_FILE = PROJECT_ROOT / "config.json"
    
    def __init__(self):
        """初始化配置，设置默认值"""
        # 目录配置
        self.RAW_FILES_DIR = self.PROJECT_ROOT / "raw_files"
        self.PARSED_TEXT_DIR = self.PROJECT_ROOT / "parsed_text"
        self.SEGMENTS_DIR = self.PROJECT_ROOT / "segments"
        self.SUMMARIES_DIR = self.PROJECT_ROOT / "summaries"
        self.CHUNKS_DIR = self.PROJECT_ROOT / "chunks"
        self.EMBEDDINGS_DIR = self.PROJECT_ROOT / "embeddings"
        self.TOOLS_DIR = self.PROJECT_ROOT / "tools"
        self.RESULTS_DIR = self.PROJECT_ROOT / "processed_results"
        
        # 文件解析配置
        self.SUPPORTED_EXTENSIONS = ['.pdf', '.docx', '.doc', '.txt']
        self.DEFAULT_ENCODING = 'utf-8'
        self.FALLBACK_ENCODINGS = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        
        # 文本切分配置
        self.DEFAULT_CHUNK_SIZE = 1000  # 字符数
        self.DEFAULT_OVERLAP = 100      # 重叠字符数
        self.MIN_CHUNK_SIZE = 100       # 最小块大小
        self.MAX_CHUNK_SIZE = 4000      # 最大块大小
        
        # 章节识别模式
        self.CHAPTER_PATTERNS = [
            r'^第[一二三四五六七八九十\d]+章.*',
            r'^Chapter\s+\d+.*',
            r'^第[一二三四五六七八九十\d]+节.*',
            r'^Section\s+\d+.*',
            r'^[一二三四五六七八九十\d]+\..*',
            r'^##?\s+.*',  # Markdown标题
            r'^\d+\.\d+.*',  # 数字编号
        ]
        
        # 摘要生成配置
        self.SUMMARY_MAX_LENGTH = 300
        self.SUMMARY_MIN_LENGTH = 50
        self.SUMMARY_TEMPERATURE = 0.3
        self.SUMMARY_TOP_P = 0.9
        
        # 默认摘要提示词
        self.DEFAULT_SUMMARY_PROMPT = """请为以下文本生成一个简洁的摘要，要求：
1. 概括主要内容和核心观点
2. 保持逻辑清晰，语言简洁
3. 长度控制在200-300字
4. 保留重要的关键信息

文本内容：
{text}

摘要："""
        
        # 向量化配置
        self.DEFAULT_EMBEDDING_MODEL = "Qwen/Qwen3-Embedding-0.6B"
        self.EMBEDDING_DIMENSION = 1024  # 默认向量维度
        self.EMBEDDING_BATCH_SIZE = 32   # 批处理大小
        self.USE_LOCAL_EMBEDDING = True  # 默认使用本地模型
        
        # 检索配置
        self.DEFAULT_TOP_K = 5           # 默认返回结果数
        self.USE_FAISS = True            # 默认使用FAISS
        self.SIMILARITY_THRESHOLD = 0.5  # 相似度阈值
        
        # API配置
        self.API_TIMEOUT = 60            # API超时时间（秒）
        self.API_RETRY_TIMES = 3         # API重试次数
        self.API_RETRY_DELAY = 1         # API重试延迟（秒）
        
        # 日志配置
        self.LOG_LEVEL = "INFO"
        self.LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        self.LOG_FILE = str(self.PROJECT_ROOT / "logs" / "preprocessor.log")
        
        # 性能配置
        self.MAX_WORKERS = 4             # 最大并发数
        self.MEMORY_LIMIT_GB = 8         # 内存限制（GB）
        
        # API密钥配置
        self.OPENAI_API_KEY = "sk-7251d850d7274b2f90adafcec54ce47f"
        self.OPENAI_BASE_URL = "https://api.deepseek.com/v1"
        self.OPENAI_MODEL = "deepseek-reasoner"
        self.DASHSCOPE_API_KEY = None
        self.DASHSCOPE_BASE_URL = "https://dashscope.aliyuncs.com/api/v1"
        self.CUSTOM_API_KEY = None
        self.CUSTOM_BASE_URL = None
        self.CUSTOM_MODEL = None
        
        # 加载配置文件（如果存在）
        self.load_from_json()
    
    def load_from_json(self):
        """从JSON文件加载配置"""
        if self.CONFIG_FILE.exists():
            try:
                with open(self.CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置
                for key, value in config_data.items():
                    if hasattr(self, key):
                        # 处理路径类型
                        if key.endswith('_DIR') or key == 'LOG_FILE':
                            if isinstance(value, str):
                                setattr(self, key, Path(value) if key.endswith('_DIR') else value)
                            else:
                                setattr(self, key, value)
                        else:
                            setattr(self, key, value)
                
                print(f"配置已从 {self.CONFIG_FILE} 加载")
                
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                print("使用默认配置")
        else:
            # 如果配置文件不存在，创建默认配置文件
            self.save_to_json()
    
    def save_to_json(self):
        """保存配置到JSON文件"""
        try:
            # 确保目录存在
            self.CONFIG_FILE.parent.mkdir(exist_ok=True)
            
            # 准备配置数据
            config_data = {}
            
            # 获取所有配置属性
            for attr_name in dir(self):
                if not attr_name.startswith('_') and not callable(getattr(self, attr_name)):
                    attr_value = getattr(self, attr_name)
                    
                    # 跳过类属性
                    if attr_name in ['PROJECT_ROOT', 'CONFIG_FILE']:
                        continue
                    
                    # 处理路径类型
                    if isinstance(attr_value, Path):
                        config_data[attr_name] = str(attr_value)
                    else:
                        config_data[attr_name] = attr_value
            
            # 保存到文件
            with open(self.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"配置已保存到 {self.CONFIG_FILE}")
            
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def update_config(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                print(f"警告: 未知的配置参数 {key}")
        
        # 保存更新后的配置
        self.save_to_json()
    
    def get_config(self, key: str):
        """获取配置参数"""
        return getattr(self, key, None)
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.RAW_FILES_DIR,
            self.PARSED_TEXT_DIR,
            self.SEGMENTS_DIR,
            self.SUMMARIES_DIR,
            self.CHUNKS_DIR,
            self.EMBEDDINGS_DIR,
            self.TOOLS_DIR,
            self.RESULTS_DIR,
            Path(self.LOG_FILE).parent
        ]
        
        for directory in directories:
            directory.mkdir(exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'directories': {
                'raw_files': str(self.RAW_FILES_DIR),
                'parsed_text': str(self.PARSED_TEXT_DIR),
                'segments': str(self.SEGMENTS_DIR),
                'summaries': str(self.SUMMARIES_DIR),
                'chunks': str(self.CHUNKS_DIR),
                'embeddings': str(self.EMBEDDINGS_DIR),
                'results': str(self.RESULTS_DIR)
            },
            'text_processing': {
                'chunk_size': self.DEFAULT_CHUNK_SIZE,
                'overlap': self.DEFAULT_OVERLAP,
                'min_chunk_size': self.MIN_CHUNK_SIZE,
                'max_chunk_size': self.MAX_CHUNK_SIZE
            },
            'embedding': {
                'model': self.DEFAULT_EMBEDDING_MODEL,
                'dimension': self.EMBEDDING_DIMENSION,
                'batch_size': self.EMBEDDING_BATCH_SIZE,
                'use_local': self.USE_LOCAL_EMBEDDING
            },
            'retrieval': {
                'top_k': self.DEFAULT_TOP_K,
                'use_faiss': self.USE_FAISS,
                'similarity_threshold': self.SIMILARITY_THRESHOLD
            },
            'api': {
                'timeout': self.API_TIMEOUT,
                'retry_times': self.API_RETRY_TIMES,
                'retry_delay': self.API_RETRY_DELAY
            },
            'api_keys': {
                'openai_api_key': self.OPENAI_API_KEY,
                'openai_base_url': self.OPENAI_BASE_URL,
                'openai_model': self.OPENAI_MODEL,
                'dashscope_api_key': self.DASHSCOPE_API_KEY,
                'dashscope_base_url': self.DASHSCOPE_BASE_URL,
                'custom_api_key': self.CUSTOM_API_KEY,
                'custom_base_url': self.CUSTOM_BASE_URL,
                'custom_model': self.CUSTOM_MODEL
            }
        }


class APIConfig:
    """
    API配置类，从主配置中获取API相关配置
    """
    
    def __init__(self, config: Config):
        self.config = config
    
    @property
    def openai_api_key(self):
        return self.config.OPENAI_API_KEY
    
    @property
    def openai_base_url(self):
        return self.config.OPENAI_BASE_URL
    
    @property
    def openai_model(self):
        return self.config.OPENAI_MODEL
    
    @property
    def dashscope_api_key(self):
        return self.config.DASHSCOPE_API_KEY
    
    @property
    def dashscope_base_url(self):
        return self.config.DASHSCOPE_BASE_URL
    
    @property
    def custom_api_key(self):
        return self.config.CUSTOM_API_KEY
    
    @property
    def custom_base_url(self):
        return self.config.CUSTOM_BASE_URL
    
    @property
    def custom_model(self):
        return self.config.CUSTOM_MODEL
    
    def get_embedding_config(self) -> Dict[str, Any]:
        """获取嵌入模型配置"""
        return {
            'api_key': self.dashscope_api_key or self.custom_api_key,
            'base_url': self.dashscope_base_url or self.custom_base_url,
            'model': 'text-embedding-v4'  # 阿里云默认模型
        }
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取大语言模型配置"""
        return {
            'api_key': self.openai_api_key or self.custom_api_key,
            'base_url': self.openai_base_url or self.custom_base_url,
            'model': self.openai_model or self.custom_model or 'gpt-3.5-turbo'
        }


# 全局配置实例
config = Config()
api_config = APIConfig(config)

# 创建必要目录
config.create_directories()


if __name__ == "__main__":
    # 打印配置信息
    import json
    print("当前配置:")
    print(json.dumps(config.to_dict(), indent=2, ensure_ascii=False))
    
    print("\nAPI配置:")
    print(f"嵌入模型配置: {api_config.get_embedding_config()}")
    print(f"LLM配置: {api_config.get_llm_config()}")
    
    # 演示配置更新
    print("\n演示配置更新:")
    config.update_config(
        DEFAULT_CHUNK_SIZE=1500,
        OPENAI_API_KEY="sk-test-key",
        USE_LOCAL_EMBEDDING=False
    )
    print(f"更新后的块大小: {config.DEFAULT_CHUNK_SIZE}")
    print(f"更新后的API密钥: {config.OPENAI_API_KEY}")
    print(f"更新后的本地嵌入设置: {config.USE_LOCAL_EMBEDDING}")
