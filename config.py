"""
配置文件
包含文本预处理模块的各种配置参数
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional


class Config:
    """
    配置类，包含所有配置参数
    """
    
    # 项目根目录
    PROJECT_ROOT = Path(__file__).parent
    
    # 目录配置
    RAW_FILES_DIR = PROJECT_ROOT / "raw_files"
    PARSED_TEXT_DIR = PROJECT_ROOT / "parsed_text"
    SEGMENTS_DIR = PROJECT_ROOT / "segments"
    SUMMARIES_DIR = PROJECT_ROOT / "summaries"
    CHUNKS_DIR = PROJECT_ROOT / "chunks"
    EMBEDDINGS_DIR = PROJECT_ROOT / "embeddings"
    TOOLS_DIR = PROJECT_ROOT / "tools"
    RESULTS_DIR = PROJECT_ROOT / "processed_results"
    
    # 文件解析配置
    SUPPORTED_EXTENSIONS = {'.pdf', '.docx', '.doc', '.txt'}
    DEFAULT_ENCODING = 'utf-8'
    FALLBACK_ENCODINGS = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    
    # 文本切分配置
    DEFAULT_CHUNK_SIZE = 1000  # 字符数
    DEFAULT_OVERLAP = 100      # 重叠字符数
    MIN_CHUNK_SIZE = 100       # 最小块大小
    MAX_CHUNK_SIZE = 4000      # 最大块大小
    
    # 章节识别模式
    CHAPTER_PATTERNS = [
        r'^第[一二三四五六七八九十\d]+章.*',
        r'^Chapter\s+\d+.*',
        r'^第[一二三四五六七八九十\d]+节.*',
        r'^Section\s+\d+.*',
        r'^[一二三四五六七八九十\d]+\..*',
        r'^##?\s+.*',  # Markdown标题
        r'^\d+\.\d+.*',  # 数字编号
    ]
    
    # 摘要生成配置
    SUMMARY_MAX_LENGTH = 300
    SUMMARY_MIN_LENGTH = 50
    SUMMARY_TEMPERATURE = 0.3
    SUMMARY_TOP_P = 0.9
    
    # 默认摘要提示词
    DEFAULT_SUMMARY_PROMPT = """请为以下文本生成一个简洁的摘要，要求：
1. 概括主要内容和核心观点
2. 保持逻辑清晰，语言简洁
3. 长度控制在200-300字
4. 保留重要的关键信息

文本内容：
{text}

摘要："""
    
    # 向量化配置
    DEFAULT_EMBEDDING_MODEL = "Qwen/Qwen3-Embedding-0.6B"
    EMBEDDING_DIMENSION = 1024  # 默认向量维度
    EMBEDDING_BATCH_SIZE = 32   # 批处理大小
    USE_LOCAL_EMBEDDING = True  # 默认使用本地模型
    
    # 检索配置
    DEFAULT_TOP_K = 5           # 默认返回结果数
    USE_FAISS = True            # 默认使用FAISS
    SIMILARITY_THRESHOLD = 0.5  # 相似度阈值
    
    # API配置
    API_TIMEOUT = 60            # API超时时间（秒）
    API_RETRY_TIMES = 3         # API重试次数
    API_RETRY_DELAY = 1         # API重试延迟（秒）
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = PROJECT_ROOT / "logs" / "preprocessor.log"
    
    # 性能配置
    MAX_WORKERS = 4             # 最大并发数
    MEMORY_LIMIT_GB = 8         # 内存限制（GB）
    
    @classmethod
    def from_env(cls) -> 'Config':
        """
        从环境变量创建配置
        
        Returns:
            配置实例
        """
        config = cls()
        
        # 从环境变量读取配置
        config.DEFAULT_CHUNK_SIZE = int(os.getenv('CHUNK_SIZE', config.DEFAULT_CHUNK_SIZE))
        config.DEFAULT_OVERLAP = int(os.getenv('OVERLAP', config.DEFAULT_OVERLAP))
        config.USE_LOCAL_EMBEDDING = os.getenv('USE_LOCAL_EMBEDDING', 'true').lower() == 'true'
        config.DEFAULT_EMBEDDING_MODEL = os.getenv('EMBEDDING_MODEL', config.DEFAULT_EMBEDDING_MODEL)
        config.USE_FAISS = os.getenv('USE_FAISS', 'true').lower() == 'true'
        config.LOG_LEVEL = os.getenv('LOG_LEVEL', config.LOG_LEVEL)
        
        return config
    
    @classmethod
    def create_directories(cls):
        """
        创建必要的目录
        """
        directories = [
            cls.RAW_FILES_DIR,
            cls.PARSED_TEXT_DIR,
            cls.SEGMENTS_DIR,
            cls.SUMMARIES_DIR,
            cls.CHUNKS_DIR,
            cls.EMBEDDINGS_DIR,
            cls.TOOLS_DIR,
            cls.RESULTS_DIR,
            cls.LOG_FILE.parent
        ]
        
        for directory in directories:
            directory.mkdir(exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            配置字典
        """
        return {
            'directories': {
                'raw_files': str(self.RAW_FILES_DIR),
                'parsed_text': str(self.PARSED_TEXT_DIR),
                'segments': str(self.SEGMENTS_DIR),
                'summaries': str(self.SUMMARIES_DIR),
                'chunks': str(self.CHUNKS_DIR),
                'embeddings': str(self.EMBEDDINGS_DIR),
                'results': str(self.RESULTS_DIR)
            },
            'text_processing': {
                'chunk_size': self.DEFAULT_CHUNK_SIZE,
                'overlap': self.DEFAULT_OVERLAP,
                'min_chunk_size': self.MIN_CHUNK_SIZE,
                'max_chunk_size': self.MAX_CHUNK_SIZE
            },
            'embedding': {
                'model': self.DEFAULT_EMBEDDING_MODEL,
                'dimension': self.EMBEDDING_DIMENSION,
                'batch_size': self.EMBEDDING_BATCH_SIZE,
                'use_local': self.USE_LOCAL_EMBEDDING
            },
            'retrieval': {
                'top_k': self.DEFAULT_TOP_K,
                'use_faiss': self.USE_FAISS,
                'similarity_threshold': self.SIMILARITY_THRESHOLD
            },
            'api': {
                'timeout': self.API_TIMEOUT,
                'retry_times': self.API_RETRY_TIMES,
                'retry_delay': self.API_RETRY_DELAY
            }
        }


class APIConfig:
    """
    API配置类
    """
    
    def __init__(self):
        # OpenAI配置
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openai_base_url = os.getenv('OPENAI_BASE_URL')
        self.openai_model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        
        # 阿里云配置
        self.dashscope_api_key = os.getenv('DASHSCOPE_API_KEY')
        self.dashscope_base_url = os.getenv('DASHSCOPE_BASE_URL')
        
        # 自定义API配置
        self.custom_api_key = os.getenv('CUSTOM_API_KEY')
        self.custom_base_url = os.getenv('CUSTOM_BASE_URL')
        self.custom_model = os.getenv('CUSTOM_MODEL')
    
    def get_embedding_config(self) -> Dict[str, Any]:
        """
        获取嵌入模型配置
        
        Returns:
            嵌入配置字典
        """
        return {
            'api_key': self.dashscope_api_key or self.custom_api_key,
            'base_url': self.dashscope_base_url or self.custom_base_url,
            'model': 'text-embedding-v4'  # 阿里云默认模型
        }
    
    def get_llm_config(self) -> Dict[str, Any]:
        """
        获取大语言模型配置
        
        Returns:
            LLM配置字典
        """
        return {
            'api_key': self.openai_api_key or self.custom_api_key,
            'base_url': self.openai_base_url or self.custom_base_url,
            'model': self.openai_model or self.custom_model or 'gpt-3.5-turbo'
        }


# 全局配置实例
config = Config.from_env()
api_config = APIConfig()

# 创建必要目录
Config.create_directories()


if __name__ == "__main__":
    # 打印配置信息
    import json
    print("当前配置:")
    print(json.dumps(config.to_dict(), indent=2, ensure_ascii=False))
    
    print("\nAPI配置:")
    print(f"嵌入模型配置: {api_config.get_embedding_config()}")
    print(f"LLM配置: {api_config.get_llm_config()}")
