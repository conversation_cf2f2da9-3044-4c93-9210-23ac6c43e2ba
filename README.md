# 文本预处理模块

本项目是一个完整的文本预处理模块，严格按照设计文档实现，支持文件解析、文本分章切分、摘要生成、向量化和检索等功能。

## 功能特性

### 📄 文件解析
- **PDF文档**: 使用 `pdfplumber` 库解析PDF文件，提取文本内容和页面信息
- **Word文档**: 使用 `python-docx` 库解析DOCX文件，保留标题样式信息
- **纯文本**: 支持多种编码格式的TXT文件解析

### 📚 文本切分
- **章节切分**: 基于标题识别和语义分段的章节级切分
- **细粒度切分**: 支持自定义块大小和重叠的细粒度切分
- **智能识别**: 自动识别章节标题和文档结构

### 📝 摘要生成
- **章节摘要**: 为每个章节生成简洁摘要
- **整体摘要**: 基于章节摘要生成文档整体摘要
- **多模型支持**: 支持OpenAI GPT、自定义API等多种大语言模型

### 🔍 向量化
- **本地模型**: 支持Qwen3-Embedding-4B等本地嵌入模型
- **云端API**: 支持阿里云通义千问、OpenAI等云端向量化服务
- **批量处理**: 高效的批量向量化处理

### 🔎 检索功能
- **向量检索**: 基于FAISS或基础相似度计算的向量检索
- **语义搜索**: 支持自然语言查询的语义搜索
- **结果排序**: 按相似度排序的检索结果

## 项目结构

```
preprocess_module/
├── raw_files/          # 原始输入文件
├── parsed_text/        # 解析后的纯文本文件
├── segments/           # 章节切分结果
├── summaries/          # 摘要文件
├── chunks/             # 细粒度切分结果
├── embeddings/         # 向量文件
├── tools/              # 工具类
│   ├── parser.py       # 文件解析器
│   ├── splitter.py     # 文本切分器
│   ├── summarizer.py   # 摘要生成器
│   ├── embedding_tool.py # 向量化工具
│   └── retriever.py    # 检索器
├── processed_results/  # 处理结果
├── text_preprocessor.py # 主预处理流程
├── config.py          # 配置文件
├── requirements.txt   # 依赖包
├── example.py         # 使用示例
└── README.md          # 说明文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 环境配置

#### 快速配置

```bash
# 复制配置模板
cp .env.example .env

# 安装dotenv支持（推荐）
pip install python-dotenv

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

#### 基础配置示例

```bash
# 基础设置
CHUNK_SIZE=1000
OVERLAP=100
USE_LOCAL_EMBEDDING=true
USE_FAISS=true
LOG_LEVEL=INFO

# OpenAI API（可选）
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 阿里云通义千问（可选）
DASHSCOPE_API_KEY=sk-your-dashscope-api-key

# 自定义API服务（可选）
CUSTOM_API_KEY=your-custom-api-key
CUSTOM_BASE_URL=http://localhost:8000/v1
CUSTOM_MODEL=your-model-name
```

详细配置说明请参考 [配置指南](CONFIGURATION.md)。

### 3. 初始化目录

运行配置文件创建必要目录：

```bash
python config.py
```

## 快速开始

### 基本使用

```python
from text_preprocessor import TextPreprocessor

# 创建预处理器
preprocessor = TextPreprocessor()

# 处理单个文件
result = preprocessor.process_file(
    file_path="raw_files/document.pdf",
    generate_summary=True,
    generate_embeddings=True,
    chunk_size=1000,
    overlap=100
)

# 搜索相关内容
results = preprocessor.search("文本处理", top_k=5)
```

### 批量处理

```python
# 批量处理目录中的文件
results = preprocessor.process_directory(
    directory_path="raw_files",
    generate_summary=True,
    generate_embeddings=True
)
```

### 保存和加载索引

```python
# 保存索引
preprocessor.save_retriever_index("my_index")

# 加载索引
new_preprocessor = TextPreprocessor()
new_preprocessor.load_retriever_index("my_index")
```

## 详细示例

运行示例脚本查看完整功能演示：

```bash
python example.py
```

示例包括：
1. 单个文件处理
2. 搜索功能演示
3. 批量处理
4. 统计信息查看
5. 索引保存和加载

## 工具类详解

### Parser类 - 文件解析器

```python
from tools.parser import Parser

parser = Parser(output_dir="parsed_text")
result = parser.parse("document.pdf")
```

支持的文件格式：
- PDF (.pdf)
- Word文档 (.docx, .doc)
- 纯文本 (.txt)

### Splitter类 - 文本切分器

```python
from tools.splitter import Splitter

splitter = Splitter()

# 章节切分
segments = splitter.split_to_chapters(text, source_name)

# 细粒度切分
chunks = splitter.split_to_chunks(text, source_name, chunk_size=1000)
```

### Summarizer类 - 摘要生成器

```python
from tools.summarizer import Summarizer

summarizer = Summarizer(api_key="your_api_key")

# 生成单个摘要
result = summarizer.summarize(text)

# 批量生成摘要
segments_with_summaries = summarizer.summarize_segments(segments, source_name)
```

### EmbeddingTool类 - 向量化工具

```python
from tools.embedding_tool import EmbeddingTool

# 使用本地模型
embedding_tool = EmbeddingTool(use_local=True)

# 使用API服务
embedding_tool = EmbeddingTool(
    use_local=False,
    api_key="your_api_key",
    base_url="your_api_url"
)

# 生成向量
embeddings = embedding_tool.embed(["文本1", "文本2"])
```

### Retriever类 - 检索器

```python
from tools.retriever import Retriever

retriever = Retriever(use_faiss=True)

# 添加向量
retriever.add_vectors(vectors, metadata)

# 查询
results = retriever.query(query_vector, top_k=5)

# 文本搜索
results = retriever.search_text(query_text, embedding_tool)
```

## 配置说明

### 配置方式

1. **环境变量文件**（推荐）
   ```bash
   cp .env.example .env
   # 编辑 .env 文件设置配置
   ```

2. **系统环境变量**
   ```bash
   export OPENAI_API_KEY=your-api-key
   export CHUNK_SIZE=1000
   ```

3. **代码中配置**
   ```python
   preprocessor = TextPreprocessor(
       use_local_embedding=True,
       api_key="your-api-key"
   )
   ```

### 主要配置参数

- `CHUNK_SIZE`: 文本块大小（默认1000字符）
- `OVERLAP`: 块重叠大小（默认100字符）
- `USE_LOCAL_EMBEDDING`: 是否使用本地嵌入模型
- `EMBEDDING_MODEL`: 嵌入模型名称
- `USE_FAISS`: 是否使用FAISS索引
- `LOG_LEVEL`: 日志级别

### 使用场景配置

#### 完全本地部署
```bash
USE_LOCAL_EMBEDDING=true
USE_FAISS=true
# 无需API密钥
```

#### 使用OpenAI API
```bash
USE_LOCAL_EMBEDDING=false
OPENAI_API_KEY=sk-your-key
OPENAI_MODEL=gpt-3.5-turbo
```

#### 混合部署
```bash
USE_LOCAL_EMBEDDING=true  # 本地嵌入
OPENAI_API_KEY=sk-your-key  # 云端摘要
```

详细配置说明请参考 [配置指南](CONFIGURATION.md)。

## 性能优化

### 本地部署建议

1. **使用本地嵌入模型**: 避免API调用延迟
2. **启用FAISS**: 提高大规模向量检索性能
3. **调整批处理大小**: 根据内存情况调整 `EMBEDDING_BATCH_SIZE`

### 云端部署建议

1. **使用云端API**: 减少本地计算资源需求
2. **配置API重试**: 提高稳定性
3. **合理设置超时**: 避免长时间等待

## 故障排除

### 常见问题

1. **依赖安装失败**: 检查Python版本和网络连接
2. **模型下载慢**: 配置镜像源或使用API服务
3. **内存不足**: 减少批处理大小或使用云端API
4. **API调用失败**: 检查API密钥和网络连接

### 日志查看

日志文件位置：`logs/preprocessor.log`

```python
# 调整日志级别
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## 扩展开发

### 添加新的文件格式支持

在 `Parser` 类中添加新的解析方法：

```python
def _parse_new_format(self, file_path: Path) -> Dict[str, Any]:
    # 实现新格式解析逻辑
    pass
```

### 自定义切分策略

继承 `Splitter` 类并重写切分方法：

```python
class CustomSplitter(Splitter):
    def _split_by_custom_rules(self, text: str) -> List[Dict[str, Any]]:
        # 实现自定义切分逻辑
        pass
```

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交Issue和Pull Request来改进本项目。

## 联系方式

如有问题或建议，请通过Issue联系。
