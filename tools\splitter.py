"""
文本切分器类
支持章节切分和细粒度切分两种模式，包含标题识别和语义分段功能
"""

import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import json


class Splitter:
    """
    文本切分器类，负责文本切分逻辑，支持章节切分和细粒度切分两种模式
    """
    
    def __init__(self, segments_dir: str = "segments", chunks_dir: str = "chunks"):
        """
        初始化切分器
        
        Args:
            segments_dir: 章节切分结果保存目录
            chunks_dir: 细粒度切分结果保存目录
        """
        self.segments_dir = Path(segments_dir)
        self.chunks_dir = Path(chunks_dir)
        self.segments_dir.mkdir(exist_ok=True)
        self.chunks_dir.mkdir(exist_ok=True)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 章节标题模式
        self.chapter_patterns = [
            r'^第[一二三四五六七八九十\d]+章.*',
            r'^Chapter\s+\d+.*',
            r'^第[一二三四五六七八九十\d]+节.*',
            r'^Section\s+\d+.*',
            r'^[一二三四五六七八九十\d]+\..*',
            r'^##?\s+.*',  # Markdown标题
            r'^\d+\.\d+.*',  # 数字编号
        ]
    
    def split_to_chapters(self, text: str, source_name: str, 
                         use_headings: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """
        按章节切分文本
        
        Args:
            text: 要切分的文本
            source_name: 源文件名（不含扩展名）
            use_headings: 从文档解析得到的标题信息
            
        Returns:
            章节列表，每个章节包含text、title、index等信息
        """
        segments = []
        
        if use_headings:
            # 使用文档结构信息进行切分
            segments = self._split_by_headings(text, use_headings)
        else:
            # 使用模式匹配进行切分
            segments = self._split_by_patterns(text)
        
        # 保存切分结果
        for i, segment in enumerate(segments):
            segment['index'] = i + 1
            segment['source_name'] = source_name
            
            # 保存到文件
            output_file = self.segments_dir / f"{source_name}_seg{i+1}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(segment['text'])
            
            segment['file_path'] = str(output_file)
        
        # 保存元数据
        metadata_file = self.segments_dir / f"{source_name}_segments_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(segments, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"章节切分完成: {source_name}, 共{len(segments)}个章节")
        return segments
    
    def _split_by_headings(self, text: str, headings: List[Dict]) -> List[Dict[str, Any]]:
        """
        根据文档标题信息切分文本
        
        Args:
            text: 文本内容
            headings: 标题信息列表
            
        Returns:
            切分后的章节列表
        """
        segments = []
        lines = text.split('\n')
        current_segment = []
        current_title = "前言"
        
        for line in lines:
            line_stripped = line.strip()
            
            # 检查是否为标题
            is_heading = False
            for heading in headings:
                if heading['text'] in line_stripped:
                    # 保存当前段落
                    if current_segment:
                        segments.append({
                            'title': current_title,
                            'text': '\n'.join(current_segment).strip()
                        })
                    
                    # 开始新段落
                    current_title = heading['text']
                    current_segment = [line]
                    is_heading = True
                    break
            
            if not is_heading:
                current_segment.append(line)
        
        # 保存最后一个段落
        if current_segment:
            segments.append({
                'title': current_title,
                'text': '\n'.join(current_segment).strip()
            })
        
        return segments
    
    def _split_by_patterns(self, text: str) -> List[Dict[str, Any]]:
        """
        根据模式匹配切分文本
        
        Args:
            text: 文本内容
            
        Returns:
            切分后的章节列表
        """
        segments = []
        lines = text.split('\n')
        current_segment = []
        current_title = "前言"
        
        for line in lines:
            line_stripped = line.strip()
            
            # 检查是否匹配章节模式
            is_chapter = False
            for pattern in self.chapter_patterns:
                if re.match(pattern, line_stripped, re.IGNORECASE):
                    # 保存当前段落
                    if current_segment:
                        segments.append({
                            'title': current_title,
                            'text': '\n'.join(current_segment).strip()
                        })
                    
                    # 开始新段落
                    current_title = line_stripped
                    current_segment = [line]
                    is_chapter = True
                    break
            
            if not is_chapter:
                current_segment.append(line)
        
        # 保存最后一个段落
        if current_segment:
            segments.append({
                'title': current_title,
                'text': '\n'.join(current_segment).strip()
            })
        
        return segments
    
    def split_to_chunks(self, text: str, source_name: str, segment_index: int = 0,
                       chunk_size: int = 1000, overlap: int = 100) -> List[Dict[str, Any]]:
        """
        细粒度切分文本
        
        Args:
            text: 要切分的文本
            source_name: 源文件名
            segment_index: 段落索引（如果是从章节切分来的）
            chunk_size: 目标块大小（字符数）
            overlap: 重叠大小（字符数）
            
        Returns:
            细分块列表
        """
        chunks = []
        
        # 首先按段落分割
        paragraphs = self._split_paragraphs(text)
        
        current_chunk = ""
        chunk_index = 1
        
        for para in paragraphs:
            # 如果当前块加上新段落不超过限制，则添加
            if len(current_chunk) + len(para) <= chunk_size:
                current_chunk += para + "\n\n"
            else:
                # 保存当前块
                if current_chunk.strip():
                    chunk_info = self._save_chunk(
                        current_chunk.strip(), source_name, 
                        segment_index, chunk_index
                    )
                    chunks.append(chunk_info)
                    chunk_index += 1
                
                # 开始新块，考虑重叠
                if overlap > 0 and current_chunk:
                    overlap_text = current_chunk[-overlap:]
                    current_chunk = overlap_text + para + "\n\n"
                else:
                    current_chunk = para + "\n\n"
        
        # 保存最后一个块
        if current_chunk.strip():
            chunk_info = self._save_chunk(
                current_chunk.strip(), source_name, 
                segment_index, chunk_index
            )
            chunks.append(chunk_info)
        
        # 保存元数据
        metadata_file = self.chunks_dir / f"{source_name}_seg{segment_index}_chunks_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(chunks, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"细粒度切分完成: {source_name}_seg{segment_index}, 共{len(chunks)}个块")
        return chunks
    
    def _split_paragraphs(self, text: str) -> List[str]:
        """
        按段落分割文本
        
        Args:
            text: 文本内容
            
        Returns:
            段落列表
        """
        # 按双换行分割段落
        paragraphs = re.split(r'\n\s*\n', text)
        
        # 清理空段落
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        return paragraphs
    
    def _save_chunk(self, chunk_text: str, source_name: str, 
                   segment_index: int, chunk_index: int) -> Dict[str, Any]:
        """
        保存文本块
        
        Args:
            chunk_text: 块文本
            source_name: 源文件名
            segment_index: 段落索引
            chunk_index: 块索引
            
        Returns:
            块信息字典
        """
        if segment_index > 0:
            filename = f"{source_name}_seg{segment_index}_chunk{chunk_index}.txt"
        else:
            filename = f"{source_name}_chunk{chunk_index}.txt"
        
        output_file = self.chunks_dir / filename
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(chunk_text)
        
        return {
            'index': chunk_index,
            'segment_index': segment_index,
            'source_name': source_name,
            'text': chunk_text,
            'file_path': str(output_file),
            'char_count': len(chunk_text)
        }


if __name__ == "__main__":
    # 测试代码
    splitter = Splitter()
    
    # 示例用法
    print("Splitter类已创建")
    print("使用方法:")
    print("1. 章节切分: splitter.split_to_chapters(text, source_name)")
    print("2. 细粒度切分: splitter.split_to_chunks(text, source_name)")
