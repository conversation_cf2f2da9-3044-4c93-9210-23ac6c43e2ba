# 文本预处理模块设计文档

本文档详细描述了文本预处理模块的设计思路，包括文件解析、文本分章切分、摘要生成与向量化、细粒度切分以及工具类设计等内容，为后续代码实现提供指导。

## 支持的文件格式与解析

·    **PDF** **文档：** 使用 pdfplumber 等 Python 库解析PDF。通过 pdfplumber.open(filepath) 打开PDF后，遍历 pdf.pages 并调用 page.extract_text() 提取每页文本[[1\]](https://blog.csdn.net/milasdf/article/details/118159902#:~:text=pdfplumber库中提供了一个extract_text)。根据需要，可处理所有页或指定页，提取纯文本内容。

·    **Word** **文档：** 使用 python-docx 库解析 DOCX 文件[[2\]](https://www.cnblogs.com/baby123/p/16809347.html#:~:text=import docx document %3D docx.Document(,打印每一个段落的文字 print(paragraph.text)。示例代码：doc = docx.Document("file.docx")，然后遍历 doc.paragraphs 获取每个段落的文本[[2\]](https://www.cnblogs.com/baby123/p/16809347.html#:~:text=import docx document %3D docx.Document(,打印每一个段落的文字 print(paragraph.text)。对于旧式 DOC 可先用 Windows COM 将其另存为 DOCX（参考[12]中的方法），然后读取。

·    **纯文本（TXT****）：** 直接以文本方式打开和读取，按行或整体读取文本内容。

解析后，将各类文件统一转换为纯文本字符串。对于 Word 文档，可保留其标题样式（Heading）信息；对于 PDF，可识别页面结构。提取出的原始文本内容保存在统一路径（例如 parsed/ 目录），以原文件名为基础命名文本文件，方便后续处理。



## 文本按章节切分（语义分段）

在获取完整文本后，第一步是按照文档的逻辑结构（如章节）进行初步切分，确保每个块在语义上相对完整不跨章。建议采用 **标题/****章节识别** 为主要策略：

·    如果原文档包含结构信息（如 Word 中的标题样式或 PDF 大纲），优先利用这些线索进行分章。例如在 python-docx 中，可检查段落的样式（paragraph.style.name）是否为“Heading1”、“Heading2”等，从而提取章节标题并分隔段落。

·    对于无显式结构标记的文档，可以借助大语言模型或嵌入方法辅助检测章节边界（**语义分段**）。例如，可将文本按固定长度切分为多个片段（如每 4,000-8,000 tokens），逐段调用 LLM 提问“是否包含新的章节或主题转换”，或者计算各段的文本嵌入向量，寻找嵌入距离较大的转折点[[3\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=会影响检索精度。)[[4\]](https://baoyu.io/translations/rag/5-levels-of-text-splitting#:~:text=Embeddings（嵌入）能够表达字符串的语义含义。虽然它们单独使用时效果有限，但将它们与其他文本的 Embeddings 进行对比，我们就能开始理解不同数据块之间的语义关系。我想深入探讨这一特性，尝试利用 Embeddings 找出语义上相似的文本群。)。这样可以模拟人为阅读的方式，以确保每段内部语义连贯，跨段不破坏主题连续性。

·    根据最佳实践，采用**标题驱动的切分策略**：例如 Unstructured 提出的“按标题分块策略”能够识别文档结构，保留章节边界，确保一个数据块不会混合来自不同章节的内容[[5\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=,按页面分块策略（仅支持API调用）：该策略专为每一页都能传递独特信息的文档而设计，可确保来自不同页面的内容绝不会混杂在同一个分块中。当检测到一个新页面时 ，即使下一个元素可以放在之前的内容块中，也会完成现有的内容块并开始一个新的内容块。)。在实现中，可以先扫描文本中常见章节标记（如“第X章”、“Chapter X”）或利用 LLM 辅助确认章节标题，再依此分割文本。

·    若需要，也可结合分层分块：首先按章节（大块，L1 级别）切分，然后在每章内部进一步按段落或主题细分（中等块，L2 级别）。这种多级分块策略可参考RAG应用的思路（先大后小分块）[[6\]](https://baoyu.io/translations/rag/5-levels-of-text-splitting#:~:text=文本分割的五个层次)[[5\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=,按页面分块策略（仅支持API调用）：该策略专为每一页都能传递独特信息的文档而设计，可确保来自不同页面的内容绝不会混杂在同一个分块中。当检测到一个新页面时 ，即使下一个元素可以放在之前的内容块中，也会完成现有的内容块并开始一个新的内容块。)。

**结果保存**：每个初步切分的章节块独立保存为文本文件，如 sourceName_seg1.txt、sourceName_seg2.txt 等，或以 JSON 形式保存编号和内容。这样便于后续查找和处理。



## 文本概述（摘要）与向量化

对每个已切分出的章节块，调用大语言模型生成摘要（概述），并对文本或摘要进行向量化：

·    **摘要生成**：使用如 OpenAI 的 GPT 等模型生成摘要[[7\]](https://blog.csdn.net/vaidfl/article/details/145716032#:~:text=文本摘要可以分为两种类型：)。常见做法是构造提示（prompt），例如 "请为以下文本生成摘要：'{text}'"，将章节文本作为输入。摘要应尽可能涵盖该章节的核心内容。生成摘要后，可与原文存储关联，例如保存为 sourceName_seg1_summary.txt。

·    **文本嵌入（向量化）**：将每个章节块或其摘要转换为向量，用于后续检索。设计一个**Embedding** **工具类**，支持调用本地模型或云服务：

·    本地使用 Qwen3-Embedding-4B 模型（支持 100+ 语种，最大上下文长度达32k tokens）[[8\]](https://huggingface.co/Qwen/Qwen3-Embedding-4B#:~:text=Qwen3)。通过 HuggingFace 的 SentenceTransformer("Qwen/Qwen3-Embedding-4B") 载入模型，输入文本即可得到高质量文本嵌入。

·    API 调用可使用阿里云通义千问的 text-embedding-v4 服务（同属于 Qwen3-Embedding 系列，支持 100+ 主流语言）[[9\]](https://help.aliyun.com/zh/model-studio/embedding#:~:text=text)。通过其 OpenAI 兼容接口传文本即可获取嵌入向量。

·    嵌入向量产生后，可存储在向量数据库（如 FAISS、Pinecone 等）或文件中，以便检索使用。需要确保嵌入维度、数据类型一致，并按章节块编号保存向量文件或插入索引库。

通过以上步骤，得到每个章节块的摘要和向量表示，为后续的检索和回答提供基础。



## 细粒度切分与检索准备

为了满足细粒度检索的需求，对上述章节块再进行**二次切分**（细分成更小的片段）并同样进行向量化：

·    **细粒度切分**：将每个章节块分割为适合检索的小片段。推荐目标大小约为 250 个 token（约 1000 字符）[[3\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=会影响检索精度。)。可使用重叠滑窗或语言学规则来切分，例如每片段末保留 50-100 个 token 作为重叠上下文。常用的方法有句子级递归切分：首先按段落（双换行）切分，再按单换行或句号细分[[10\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=解决这一问题的方法之一是使用递归分块法，这种方法有助于保留单个句子。使用这种方法，您可以指定一个有序的分隔符列表来指导分割过程。例如，以下是一些常用的分隔符：)。切分原则是在不破坏句意的前提下，尽量让每块内容均匀且语义连贯。

·    **分块示例**：若某章节较长，可先按完整段落切分，然后检查每段长度；若仍超过期望长度，再按句子或字数切分。利用 LangChain 的 RecursiveCharacterTextSplitter 等工具也可实现类似效果，支持自定义分隔符和重叠大小。

·    **向量化处理**：对每个细分的小块，同样调用前述嵌入工具（本地 Qwen 或阿里云 API）生成向量，并保存或索引。这样每个小块都有对应的向量，方便后续检索时快速定位相关内容。

·    **检索策略**：在实际检索时，可对用户查询生成向量，与这些小块向量计算相似度，返回最相关的片段。细粒度切分能提高检索精度，避免一个大块中混杂多个主题而造成匹配模糊[[3\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=会影响检索精度。)。

**结果保存**：细分块应单独保存，如 sourceName_seg1_chunk1.txt、sourceName_seg1_chunk2.txt 等，或记录其对应编号。向量也应对应编号保存，以便检索系统加载并查询。



## 存储路径与命名规范

建议建立合理的目录结构，示例如下：
 

/project_root/
 ├─ raw_files/      # 原始输入文件（PDF、DOCX、TXT等）
 ├─ parsed_text/     # 解析出的纯文本文件，以原文件名命名
 ├─ segments/       # 一级切分后的文本（按章节）
 │  ├─ file1_seg1.txt 
 │  ├─ file1_seg2.txt 
 │  └─ ...
 ├─ summaries/      # 章节摘要文件
 │  ├─ file1_seg1_summary.txt 
 │  └─ ...
 ├─ chunks/       # 二级切分后的细粒度文本
 │  ├─ file1_seg1_chunk1.txt 
 │  ├─ file1_seg1_chunk2.txt 
 │  └─ ...
 ├─ embeddings/     # 存储嵌入向量（可选二进制或数据库）
 │  ├─ file1_seg1.vec
 │  ├─ file1_seg1_chunk1.vec
 │  └─ ...
 └─ tools/        # 工具类、日志等其他资源

命名要求清晰规范：文件名中包含原文件名、切分层级（seg/chunk）和序号。例如 BookA_seg1.txt 表示第一本书的第1章；BookA_seg1_chunk3.txt 表示该章的第3个细分块。这样便于追溯每个片段的来源。



## 工具类设计

将常用功能封装为工具类，以便复用：
 \- **Parser** **类**：负责根据文件类型调用不同解析器（PDF、DOCX、TXT），输出统一格式的文本。可实现 parse(file_path) 方法，根据扩展名选择相应库。
 \- **Splitter** **类**：负责文本切分逻辑，支持章节切分和细粒度切分两种模式。接口如 split_to_chapters(text)、split_to_chunks(text, chunk_size, overlap) 等，可内部调用正则或 LLM 接口判断边界。
 \- **Summarizer** **类**：封装调用大语言模型（如 OpenAI GPT）的摘要功能，实现 summarize(text)，通过给定提示生成摘要。
 \- **EmbeddingTool** **类**：封装向量化方法，支持参数选择本地模型或云 API。接口如 embed(text, use_local=True)，内部根据 use_local 决定调用 HuggingFace 模型或阿里云服务，返回文本向量。
 \- **Retriever** **类**：负责处理向量检索逻辑，可连接向量数据库或使用近邻搜索算法。提供 add_vectors(id, vector) 和 query(vector, top_k) 等方法，实现向量的存储和检索。

工具类之间的关系：预处理流程先使用 Parser 和 Splitter 得到文本块，然后调用 Summarizer 生成摘要，再用 EmbeddingTool 生成向量；Retriever 可在需要时载入这些向量进行相似度查询。这样的模块化设计提高可维护性，后续可在其他项目中复用。



## 总结

本文档提出了一个完整的文本预处理框架：从**文件解析**入手，得到纯文本；再进行**章节级分块**（保留语义完整性[[5\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=,按页面分块策略（仅支持API调用）：该策略专为每一页都能传递独特信息的文档而设计，可确保来自不同页面的内容绝不会混杂在同一个分块中。当检测到一个新页面时 ，即使下一个元素可以放在之前的内容块中，也会完成现有的内容块并开始一个新的内容块。)），为每块生成摘要并向量化；随后进行**细粒度分块**以满足检索需求（建议片段大小约250 tokens[[3\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=会影响检索精度。)），并对每段再次向量化；同时设计了**工具类**封装通用操作。各部分数据均按统一规范保存，便于后续调用和管理。这样的设计既考虑了语义完整性，又兼顾了检索效率，能够为基于大模型的文档处理和问答系统提供扎实的数据基础。

**参考文献：** 使用了 PDF 解析（pdfplumber）提取文本[[1\]](https://blog.csdn.net/milasdf/article/details/118159902#:~:text=pdfplumber库中提供了一个extract_text)、Python-Docx 读取 Word[[2\]](https://www.cnblogs.com/baby123/p/16809347.html#:~:text=import docx document %3D docx.Document(,打印每一个段落的文字 print(paragraph.text)、文档分块与语义分割的最佳实践[[11\]](https://baoyu.io/translations/rag/5-levels-of-text-splitting#:~:text=文本分割的五个层次)[[3\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=会影响检索精度。)[[5\]](https://developer.volcengine.com/articles/7394380719931260967#:~:text=,按页面分块策略（仅支持API调用）：该策略专为每一页都能传递独特信息的文档而设计，可确保来自不同页面的内容绝不会混杂在同一个分块中。当检测到一个新页面时 ，即使下一个元素可以放在之前的内容块中，也会完成现有的内容块并开始一个新的内容块。)、LLM 摘要生成示例[[7\]](https://blog.csdn.net/vaidfl/article/details/145716032#:~:text=文本摘要可以分为两种类型：)、Qwen3-Embedding 模型说明[[8\]](https://huggingface.co/Qwen/Qwen3-Embedding-4B#:~:text=Qwen3)和阿里云向量化服务说明[[9\]](https://help.aliyun.com/zh/model-studio/embedding#:~:text=text)等资料。