"""
配置管理工具
提供命令行界面来管理配置文件
"""

import json
import sys
from pathlib import Path
from config import Config


def show_config():
    """显示当前配置"""
    config = Config()
    print("=" * 60)
    print("当前配置")
    print("=" * 60)
    
    config_dict = config.to_dict()
    print(json.dumps(config_dict, indent=2, ensure_ascii=False))


def update_config():
    """交互式更新配置"""
    config = Config()
    
    print("=" * 60)
    print("配置更新工具")
    print("=" * 60)
    print("输入要更新的配置项，格式: 配置名=值")
    print("例如: DEFAULT_CHUNK_SIZE=1500")
    print("输入 'done' 完成配置")
    print("输入 'show' 查看当前配置")
    print("=" * 60)
    
    updates = {}
    
    while True:
        try:
            user_input = input("\n请输入配置项 (或 'done'/'show'): ").strip()
            
            if user_input.lower() == 'done':
                break
            elif user_input.lower() == 'show':
                print("\n当前配置:")
                for key, value in config.to_dict().items():
                    if isinstance(value, dict):
                        print(f"{key}:")
                        for k, v in value.items():
                            print(f"  {k}: {v}")
                    else:
                        print(f"{key}: {value}")
                continue
            elif '=' not in user_input:
                print("格式错误，请使用: 配置名=值")
                continue
            
            key, value = user_input.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            # 检查配置项是否存在
            if not hasattr(config, key):
                print(f"警告: 配置项 '{key}' 不存在")
                continue
            
            # 尝试转换值的类型
            original_value = getattr(config, key)
            if isinstance(original_value, bool):
                value = value.lower() in ('true', '1', 'yes', 'on')
            elif isinstance(original_value, int):
                value = int(value)
            elif isinstance(original_value, float):
                value = float(value)
            elif isinstance(original_value, list):
                # 简单的列表解析
                if value.startswith('[') and value.endswith(']'):
                    value = json.loads(value)
                else:
                    value = [item.strip() for item in value.split(',')]
            elif value.lower() == 'null' or value.lower() == 'none':
                value = None
            
            updates[key] = value
            print(f"已设置: {key} = {value}")
            
        except KeyboardInterrupt:
            print("\n\n操作已取消")
            return
        except Exception as e:
            print(f"错误: {e}")
    
    if updates:
        print(f"\n准备更新 {len(updates)} 个配置项:")
        for key, value in updates.items():
            print(f"  {key}: {getattr(config, key)} -> {value}")
        
        confirm = input("\n确认更新? (y/N): ").strip().lower()
        if confirm in ('y', 'yes'):
            config.update_config(**updates)
            print("配置已更新并保存!")
        else:
            print("更新已取消")
    else:
        print("没有配置项需要更新")


def set_api_keys():
    """设置API密钥"""
    config = Config()
    
    print("=" * 60)
    print("API密钥配置")
    print("=" * 60)
    
    # OpenAI配置
    print("\n1. OpenAI API配置:")
    openai_key = input(f"OpenAI API Key (当前: {config.OPENAI_API_KEY or '未设置'}): ").strip()
    if openai_key:
        config.update_config(OPENAI_API_KEY=openai_key)
    
    openai_url = input(f"OpenAI Base URL (当前: {config.OPENAI_BASE_URL}): ").strip()
    if openai_url:
        config.update_config(OPENAI_BASE_URL=openai_url)
    
    openai_model = input(f"OpenAI Model (当前: {config.OPENAI_MODEL}): ").strip()
    if openai_model:
        config.update_config(OPENAI_MODEL=openai_model)
    
    # 阿里云配置
    print("\n2. 阿里云DashScope API配置:")
    dashscope_key = input(f"DashScope API Key (当前: {config.DASHSCOPE_API_KEY or '未设置'}): ").strip()
    if dashscope_key:
        config.update_config(DASHSCOPE_API_KEY=dashscope_key)
    
    # 自定义API配置
    print("\n3. 自定义API配置:")
    custom_key = input(f"Custom API Key (当前: {config.CUSTOM_API_KEY or '未设置'}): ").strip()
    if custom_key:
        config.update_config(CUSTOM_API_KEY=custom_key)
    
    custom_url = input(f"Custom Base URL (当前: {config.CUSTOM_BASE_URL or '未设置'}): ").strip()
    if custom_url:
        config.update_config(CUSTOM_BASE_URL=custom_url)
    
    custom_model = input(f"Custom Model (当前: {config.CUSTOM_MODEL or '未设置'}): ").strip()
    if custom_model:
        config.update_config(CUSTOM_MODEL=custom_model)
    
    print("\nAPI密钥配置完成!")


def reset_config():
    """重置配置为默认值"""
    print("=" * 60)
    print("重置配置")
    print("=" * 60)
    
    confirm = input("确认要重置所有配置为默认值吗? 这将删除当前的config.json文件 (y/N): ").strip().lower()
    
    if confirm in ('y', 'yes'):
        config_file = Path("config.json")
        if config_file.exists():
            config_file.unlink()
            print("配置文件已删除")
        
        # 重新创建默认配置
        config = Config()
        print("已重置为默认配置")
    else:
        print("重置已取消")


def export_config():
    """导出配置到文件"""
    config = Config()
    
    filename = input("请输入导出文件名 (默认: config_backup.json): ").strip()
    if not filename:
        filename = "config_backup.json"
    
    if not filename.endswith('.json'):
        filename += '.json'
    
    try:
        config_dict = config.to_dict()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        print(f"配置已导出到: {filename}")
    except Exception as e:
        print(f"导出失败: {e}")


def import_config():
    """从文件导入配置"""
    filename = input("请输入配置文件名: ").strip()
    
    if not Path(filename).exists():
        print(f"文件不存在: {filename}")
        return
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        config = Config()
        
        # 显示将要导入的配置
        print("\n将要导入的配置:")
        print(json.dumps(config_data, indent=2, ensure_ascii=False))
        
        confirm = input("\n确认导入这些配置吗? (y/N): ").strip().lower()
        if confirm in ('y', 'yes'):
            # 更新配置
            updates = {}
            for key, value in config_data.items():
                if hasattr(config, key):
                    updates[key] = value
                else:
                    print(f"跳过未知配置项: {key}")
            
            if updates:
                config.update_config(**updates)
                print("配置导入成功!")
            else:
                print("没有有效的配置项可导入")
        else:
            print("导入已取消")
            
    except Exception as e:
        print(f"导入失败: {e}")


def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 60)
        print("文本预处理模块 - 配置管理工具")
        print("=" * 60)
        print("1. 查看当前配置")
        print("2. 更新配置")
        print("3. 设置API密钥")
        print("4. 重置配置")
        print("5. 导出配置")
        print("6. 导入配置")
        print("0. 退出")
        print("=" * 60)
        
        try:
            choice = input("请选择操作 (0-6): ").strip()
            
            if choice == '0':
                print("再见!")
                break
            elif choice == '1':
                show_config()
            elif choice == '2':
                update_config()
            elif choice == '3':
                set_api_keys()
            elif choice == '4':
                reset_config()
            elif choice == '5':
                export_config()
            elif choice == '6':
                import_config()
            else:
                print("无效选择，请输入 0-6")
                
        except KeyboardInterrupt:
            print("\n\n再见!")
            break
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1].lower()
        if command == 'show':
            show_config()
        elif command == 'reset':
            reset_config()
        else:
            print(f"未知命令: {command}")
            print("可用命令: show, reset")
    else:
        # 交互模式
        main()
