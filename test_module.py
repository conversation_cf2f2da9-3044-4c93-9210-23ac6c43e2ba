"""
文本预处理模块测试脚本
验证各个组件的基本功能
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from tools.parser import Parser
        print("✓ Parser导入成功")
    except Exception as e:
        print(f"✗ Parser导入失败: {e}")
        return False
    
    try:
        from tools.splitter import Splitter
        print("✓ Splitter导入成功")
    except Exception as e:
        print(f"✗ Splitter导入失败: {e}")
        return False
    
    try:
        from tools.summarizer import Summarizer
        print("✓ Summarizer导入成功")
    except Exception as e:
        print(f"✗ Summarizer导入失败: {e}")
        return False
    
    try:
        from tools.embedding_tool import EmbeddingTool
        print("✓ EmbeddingTool导入成功")
    except Exception as e:
        print(f"✗ EmbeddingTool导入失败: {e}")
        return False
    
    try:
        from tools.retriever import Retriever
        print("✓ Retriever导入成功")
    except Exception as e:
        print(f"✗ Retriever导入失败: {e}")
        return False
    
    try:
        from text_preprocessor import TextPreprocessor
        print("✓ TextPreprocessor导入成功")
    except Exception as e:
        print(f"✗ TextPreprocessor导入失败: {e}")
        return False
    
    try:
        from config import config, api_config
        print("✓ Config导入成功")
    except Exception as e:
        print(f"✗ Config导入失败: {e}")
        return False
    
    return True


def test_parser():
    """测试文件解析器"""
    print("\n测试文件解析器...")
    
    try:
        from tools.parser import Parser
        
        # 创建测试文件
        test_file = Path("raw_files") / "test_parser.txt"
        test_file.parent.mkdir(exist_ok=True)
        
        test_content = """第一章 测试章节

这是一个测试文档，用于验证解析器功能。

第二章 另一个章节

包含更多测试内容。"""
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 测试解析
        parser = Parser()
        result = parser.parse(test_file)
        
        if result and 'text' in result:
            print("✓ 文件解析成功")
            print(f"  解析文本长度: {len(result['text'])}字符")
            return True
        else:
            print("✗ 文件解析失败: 结果为空")
            return False
            
    except Exception as e:
        print(f"✗ 文件解析失败: {e}")
        traceback.print_exc()
        return False


def test_splitter():
    """测试文本切分器"""
    print("\n测试文本切分器...")
    
    try:
        from tools.splitter import Splitter
        
        test_text = """第一章 引言

这是第一章的内容，介绍了基本概念。

第二章 方法

这是第二章的内容，描述了具体方法。

第三章 结论

这是第三章的内容，总结了主要结论。"""
        
        splitter = Splitter()
        
        # 测试章节切分
        segments = splitter.split_to_chapters(test_text, "test_splitter")
        
        if segments and len(segments) > 0:
            print(f"✓ 章节切分成功，共{len(segments)}个章节")
            
            # 测试细粒度切分
            chunks = splitter.split_to_chunks(test_text, "test_splitter", chunk_size=100)
            
            if chunks and len(chunks) > 0:
                print(f"✓ 细粒度切分成功，共{len(chunks)}个块")
                return True
            else:
                print("✗ 细粒度切分失败")
                return False
        else:
            print("✗ 章节切分失败")
            return False
            
    except Exception as e:
        print(f"✗ 文本切分失败: {e}")
        traceback.print_exc()
        return False


def test_embedding_tool():
    """测试向量化工具"""
    print("\n测试向量化工具...")
    
    try:
        from tools.embedding_tool import EmbeddingTool
        
        # 使用基础配置，不依赖外部模型
        embedding_tool = EmbeddingTool(use_local=False)
        
        # 测试基本功能（不实际调用API）
        print("✓ EmbeddingTool初始化成功")
        
        # 测试保存和加载功能
        import numpy as np
        test_embeddings = [np.random.rand(768) for _ in range(3)]
        test_metadata = [{'index': i, 'text': f'test{i}'} for i in range(3)]
        
        filename = embedding_tool.save_embeddings(test_embeddings, test_metadata, "test_embeddings")
        print(f"✓ 向量保存成功: {filename}")
        
        loaded_embeddings, loaded_metadata = embedding_tool.load_embeddings("test_embeddings")
        
        if len(loaded_embeddings) == len(test_embeddings):
            print("✓ 向量加载成功")
            return True
        else:
            print("✗ 向量加载失败: 数量不匹配")
            return False
            
    except Exception as e:
        print(f"✗ 向量化工具测试失败: {e}")
        traceback.print_exc()
        return False


def test_retriever():
    """测试检索器"""
    print("\n测试检索器...")
    
    try:
        from tools.retriever import Retriever
        import numpy as np
        
        # 创建检索器（不使用FAISS以避免依赖问题）
        retriever = Retriever(use_faiss=False, dimension=768)
        
        # 创建测试向量和元数据
        test_vectors = np.random.rand(5, 768)
        test_metadata = [{'index': i, 'text': f'document{i}'} for i in range(5)]
        
        # 添加向量
        retriever.add_vectors(test_vectors, test_metadata)
        print("✓ 向量添加成功")
        
        # 测试查询
        query_vector = np.random.rand(768)
        results = retriever.query(query_vector, top_k=3)
        
        if results and len(results) == 3:
            print(f"✓ 查询成功，返回{len(results)}个结果")
            
            # 测试保存和加载
            index_path = retriever.save_index("test_index")
            print(f"✓ 索引保存成功: {index_path}")
            
            new_retriever = Retriever(use_faiss=False)
            new_retriever.load_index("test_index")
            print("✓ 索引加载成功")
            
            return True
        else:
            print("✗ 查询失败")
            return False
            
    except Exception as e:
        print(f"✗ 检索器测试失败: {e}")
        traceback.print_exc()
        return False


def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from config import config, api_config
        
        # 测试配置访问
        print(f"✓ 默认块大小: {config.DEFAULT_CHUNK_SIZE}")
        print(f"✓ 使用本地嵌入: {config.USE_LOCAL_EMBEDDING}")
        print(f"✓ 嵌入模型: {config.DEFAULT_EMBEDDING_MODEL}")
        
        # 测试目录创建
        config.create_directories()
        print("✓ 目录创建成功")
        
        # 测试配置转换
        config_dict = config.to_dict()
        if config_dict and 'directories' in config_dict:
            print("✓ 配置转换成功")
            return True
        else:
            print("✗ 配置转换失败")
            return False
            
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        traceback.print_exc()
        return False


def test_text_preprocessor():
    """测试主预处理器"""
    print("\n测试主预处理器...")
    
    try:
        from text_preprocessor import TextPreprocessor
        
        # 创建预处理器（不使用外部API）
        preprocessor = TextPreprocessor(
            use_local_embedding=False,  # 避免下载模型
            api_key=None  # 不使用API
        )
        
        print("✓ TextPreprocessor初始化成功")
        
        # 测试统计功能
        stats = preprocessor.get_stats()
        if stats and 'retriever_stats' in stats:
            print("✓ 统计信息获取成功")
            return True
        else:
            print("✗ 统计信息获取失败")
            return False
            
    except Exception as e:
        print(f"✗ 主预处理器测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("=" * 60)
    print("文本预处理模块功能测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("文件解析器", test_parser),
        ("文本切分器", test_splitter),
        ("向量化工具", test_embedding_tool),
        ("检索器", test_retriever),
        ("配置", test_config),
        ("主预处理器", test_text_preprocessor),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！模块功能正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
