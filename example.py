"""
文本预处理模块使用示例
演示如何使用文本预处理模块的各种功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from text_preprocessor import TextPreprocessor
from config import config, api_config


def example_single_file():
    """
    示例1: 处理单个文件
    """
    print("=" * 50)
    print("示例1: 处理单个文件")
    print("=" * 50)
    
    # 创建预处理器实例
    preprocessor = TextPreprocessor(
        use_local_embedding=config.USE_LOCAL_EMBEDDING,
        embedding_model=config.DEFAULT_EMBEDDING_MODEL,
        api_key=api_config.openai_api_key,
        base_url=api_config.openai_base_url
    )
    
    # 检查是否有测试文件
    test_file = Path("raw_files") / "test.txt"
    if not test_file.exists():
        # 创建一个测试文件
        test_content = """
第一章 引言

本文档介绍了文本预处理的基本概念和方法。文本预处理是自然语言处理的重要步骤，包括文本清洗、分词、向量化等过程。

第二章 文本解析

文本解析是将不同格式的文档转换为纯文本的过程。支持的格式包括PDF、DOCX、TXT等。

2.1 PDF解析
PDF文档解析使用pdfplumber库，可以提取文本内容和页面信息。

2.2 DOCX解析
DOCX文档解析使用python-docx库，可以保留文档结构信息。

第三章 文本切分

文本切分包括章节切分和细粒度切分两个层次。章节切分保持语义完整性，细粒度切分便于检索。

第四章 向量化

向量化是将文本转换为数值向量的过程，支持本地模型和云端API两种方式。

第五章 检索

基于向量相似度进行文本检索，支持FAISS和基础相似度计算两种方法。
        """
        
        test_file.parent.mkdir(exist_ok=True)
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content.strip())
        print(f"创建测试文件: {test_file}")
    
    try:
        # 处理文件
        result = preprocessor.process_file(
            file_path=test_file,
            generate_summary=True,  # 生成摘要
            generate_embeddings=True,  # 生成向量
            chunk_size=500,  # 块大小
            overlap=50  # 重叠大小
        )
        
        print(f"处理完成!")
        print(f"源文件: {result['source_name']}")
        print(f"章节数: {len(result['segments'])}")
        print(f"块数: {len(result['chunks'])}")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        
        # 显示第一个章节的信息
        if result['segments']:
            first_segment = result['segments'][0]
            print(f"\n第一个章节:")
            print(f"标题: {first_segment.get('title', 'N/A')}")
            print(f"文本长度: {len(first_segment['text'])}字符")
            if 'summary' in first_segment:
                print(f"摘要: {first_segment['summary'][:100]}...")
        
        return preprocessor, result
        
    except Exception as e:
        print(f"处理失败: {str(e)}")
        return None, None


def example_search(preprocessor):
    """
    示例2: 搜索功能
    """
    if preprocessor is None:
        print("预处理器未初始化，跳过搜索示例")
        return
    
    print("\n" + "=" * 50)
    print("示例2: 搜索功能")
    print("=" * 50)
    
    # 搜索查询
    queries = [
        "文本解析",
        "向量化",
        "PDF处理",
        "检索方法"
    ]
    
    for query in queries:
        try:
            print(f"\n搜索: '{query}'")
            results = preprocessor.search(query, top_k=3)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. 相似度: {result['similarity']:.3f}")
                    print(f"     元数据: {result['metadata']}")
            else:
                print("  没有找到相关结果")
                
        except Exception as e:
            print(f"  搜索失败: {str(e)}")


def example_batch_processing():
    """
    示例3: 批量处理
    """
    print("\n" + "=" * 50)
    print("示例3: 批量处理")
    print("=" * 50)
    
    # 创建预处理器
    preprocessor = TextPreprocessor(
        use_local_embedding=False,  # 批量处理时可能使用API更稳定
        api_key=api_config.openai_api_key,
        base_url=api_config.openai_base_url
    )
    
    # 检查raw_files目录
    raw_files_dir = Path("raw_files")
    files = list(raw_files_dir.glob("*.txt"))
    
    if len(files) < 2:
        # 创建更多测试文件
        for i in range(2, 4):
            test_file = raw_files_dir / f"test{i}.txt"
            test_content = f"""
第{i}章 测试文档

这是第{i}个测试文档，用于演示批量处理功能。

{i}.1 内容概述
本文档包含了关于主题{i}的详细信息。

{i}.2 技术细节
详细的技术实现和方法说明。

{i}.3 总结
本章总结了主要观点和结论。
            """
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content.strip())
            print(f"创建测试文件: {test_file}")
    
    try:
        # 批量处理
        results = preprocessor.process_directory(
            directory_path="raw_files",
            generate_summary=False,  # 批量处理时可以跳过摘要以节省时间
            generate_embeddings=True,
            chunk_size=300,
            overlap=30
        )
        
        print(f"批量处理完成!")
        print(f"处理文件数: {len(results)}")
        
        for result in results:
            print(f"  - {result['source_name']}: {len(result['segments'])}章节, {len(result['chunks'])}块")
        
        return preprocessor
        
    except Exception as e:
        print(f"批量处理失败: {str(e)}")
        return None


def example_statistics():
    """
    示例4: 统计信息
    """
    print("\n" + "=" * 50)
    print("示例4: 统计信息")
    print("=" * 50)
    
    preprocessor = TextPreprocessor()
    
    try:
        stats = preprocessor.get_stats()
        
        print("处理统计:")
        print(f"  检索器统计: {stats['retriever_stats']}")
        print(f"  目录统计: {stats['directories']}")
        
    except Exception as e:
        print(f"获取统计信息失败: {str(e)}")


def example_save_load_index():
    """
    示例5: 保存和加载索引
    """
    print("\n" + "=" * 50)
    print("示例5: 保存和加载索引")
    print("=" * 50)
    
    preprocessor = TextPreprocessor()
    
    try:
        # 保存索引
        index_path = preprocessor.save_retriever_index("example_index")
        print(f"索引已保存到: {index_path}")
        
        # 创建新的预处理器并加载索引
        new_preprocessor = TextPreprocessor()
        new_preprocessor.load_retriever_index("example_index")
        print("索引加载成功")
        
        # 测试加载的索引
        stats = new_preprocessor.get_stats()
        print(f"加载的索引统计: {stats['retriever_stats']}")
        
    except Exception as e:
        print(f"索引操作失败: {str(e)}")


def main():
    """
    主函数，运行所有示例
    """
    print("文本预处理模块使用示例")
    print("=" * 50)
    
    # 显示配置信息
    print("当前配置:")
    print(f"  使用本地嵌入: {config.USE_LOCAL_EMBEDDING}")
    print(f"  嵌入模型: {config.DEFAULT_EMBEDDING_MODEL}")
    print(f"  默认块大小: {config.DEFAULT_CHUNK_SIZE}")
    print(f"  使用FAISS: {config.USE_FAISS}")
    
    # 运行示例
    preprocessor, result = example_single_file()
    example_search(preprocessor)
    batch_preprocessor = example_batch_processing()
    example_statistics()
    example_save_load_index()

    print("\n" + "=" * 50)
    print("所有示例运行完成!")
    print("=" * 50)

    # 提供交互式搜索
    if preprocessor:
        print("\n您可以尝试交互式搜索:")
        while True:
            try:
                query = input("\n请输入搜索查询 (输入 'quit' 退出): ").strip()
                if query.lower() in ['quit', 'exit', 'q']:
                    break
                if query:
                    results = preprocessor.search(query, top_k=3)
                    if results:
                        for i, result in enumerate(results, 1):
                            print(f"  {i}. 相似度: {result['similarity']:.3f}")
                            print(f"     元数据: {result['metadata']}")
                    else:
                        print("  没有找到相关结果")
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"  搜索错误: {str(e)}")

    print("感谢使用文本预处理模块!")


if __name__ == "__main__":
    main()
