"""
测试OpenAI API 1.x版本兼容性
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_openai_import():
    """测试OpenAI库导入"""
    print("测试OpenAI库导入...")
    
    try:
        import openai
        print(f"✓ openai库版本: {openai.__version__}")
        
        # 检查是否为1.x版本
        version_parts = openai.__version__.split('.')
        major_version = int(version_parts[0])
        
        if major_version >= 1:
            print("✓ 检测到OpenAI 1.x版本")
            
            try:
                from openai import OpenAI
                print("✓ OpenAI客户端类导入成功")
                return True
            except ImportError as e:
                print(f"✗ OpenAI客户端类导入失败: {e}")
                return False
        else:
            print(f"⚠️  检测到OpenAI 0.x版本 ({openai.__version__})，建议升级到1.x版本")
            return False
            
    except ImportError:
        print("✗ openai库未安装")
        print("请安装: pip install openai>=1.0.0")
        return False
    except Exception as e:
        print(f"✗ OpenAI库检查失败: {e}")
        return False


def test_summarizer_init():
    """测试Summarizer类初始化"""
    print("\n测试Summarizer类初始化...")
    
    try:
        from tools.summarizer import Summarizer
        
        # 测试不带API密钥的初始化
        summarizer = Summarizer(api_type="openai")
        print("✓ Summarizer初始化成功（无API密钥）")
        
        # 测试带API密钥的初始化（使用假密钥）
        summarizer_with_key = Summarizer(
            api_type="openai",
            api_key="sk-fake-key-for-testing",
            base_url="https://api.openai.com/v1"
        )
        print("✓ Summarizer初始化成功（带API密钥）")
        
        return True
        
    except Exception as e:
        print(f"✗ Summarizer初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_embedding_tool_init():
    """测试EmbeddingTool类初始化"""
    print("\n测试EmbeddingTool类初始化...")
    
    try:
        from tools.embedding_tool import EmbeddingTool
        
        # 测试不使用本地模型的初始化
        embedding_tool = EmbeddingTool(
            use_local=False,
            api_key="sk-fake-key-for-testing"
        )
        print("✓ EmbeddingTool初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ EmbeddingTool初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_call_structure():
    """测试API调用结构（不实际调用）"""
    print("\n测试API调用结构...")
    
    try:
        from tools.summarizer import Summarizer
        
        # 创建带假API密钥的实例
        summarizer = Summarizer(
            api_type="openai",
            api_key="sk-fake-key-for-testing"
        )
        
        # 检查客户端是否正确初始化
        if hasattr(summarizer, 'client') and summarizer.client is not None:
            print("✓ OpenAI客户端对象创建成功")
            
            # 检查客户端是否有正确的方法
            if hasattr(summarizer.client, 'chat'):
                print("✓ 客户端具有chat属性")
                
                if hasattr(summarizer.client.chat, 'completions'):
                    print("✓ 客户端具有completions属性")
                    
                    if hasattr(summarizer.client.chat.completions, 'create'):
                        print("✓ 客户端具有create方法")
                        print("✓ API调用结构验证成功")
                        return True
                    else:
                        print("✗ 客户端缺少create方法")
                else:
                    print("✗ 客户端缺少completions属性")
            else:
                print("✗ 客户端缺少chat属性")
        else:
            print("⚠️  OpenAI客户端未初始化（可能是API密钥问题）")
            return True  # 这是预期的，因为我们使用的是假密钥
        
        return False
        
    except Exception as e:
        print(f"✗ API调用结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_embedding_api_structure():
    """测试嵌入API调用结构"""
    print("\n测试嵌入API调用结构...")
    
    try:
        from tools.embedding_tool import EmbeddingTool
        
        # 创建带假API密钥的实例
        embedding_tool = EmbeddingTool(
            use_local=False,
            api_key="sk-fake-key-for-testing"
        )
        
        # 检查OpenAI客户端是否正确初始化
        if hasattr(embedding_tool, 'openai_client') and embedding_tool.openai_client is not None:
            print("✓ OpenAI嵌入客户端对象创建成功")
            
            # 检查客户端是否有正确的方法
            if hasattr(embedding_tool.openai_client, 'embeddings'):
                print("✓ 客户端具有embeddings属性")
                
                if hasattr(embedding_tool.openai_client.embeddings, 'create'):
                    print("✓ 客户端具有create方法")
                    print("✓ 嵌入API调用结构验证成功")
                    return True
                else:
                    print("✗ 客户端缺少create方法")
            else:
                print("✗ 客户端缺少embeddings属性")
        else:
            print("⚠️  OpenAI嵌入客户端未初始化（可能是API密钥问题）")
            return True  # 这是预期的，因为我们使用的是假密钥
        
        return False
        
    except Exception as e:
        print(f"✗ 嵌入API调用结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("=" * 60)
    print("OpenAI API 1.x版本兼容性测试")
    print("=" * 60)
    
    tests = [
        ("OpenAI库导入", test_openai_import),
        ("Summarizer初始化", test_summarizer_init),
        ("EmbeddingTool初始化", test_embedding_tool_init),
        ("API调用结构", test_api_call_structure),
        ("嵌入API调用结构", test_embedding_api_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！OpenAI API 1.x兼容性正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
