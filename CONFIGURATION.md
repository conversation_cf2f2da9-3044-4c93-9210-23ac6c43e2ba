# 配置指南

本文档详细说明如何配置文本预处理模块的各种参数和API密钥。

## 快速开始

### 1. 复制配置模板

```bash
cp .env.example .env
```

### 2. 编辑配置文件

使用文本编辑器打开 `.env` 文件，根据需要修改配置：

```bash
# Windows
notepad .env

# Linux/Mac
nano .env
# 或
vim .env
```

### 3. 安装python-dotenv（推荐）

```bash
pip install python-dotenv
```

这样可以自动加载 `.env` 文件中的配置。如果不安装，也可以手动设置系统环境变量。

## 配置分类

### 基础配置

```bash
# 文本切分参数
CHUNK_SIZE=1000          # 文本块大小（字符数）
OVERLAP=100              # 块之间的重叠大小
USE_LOCAL_EMBEDDING=true # 是否使用本地嵌入模型
USE_FAISS=true          # 是否使用FAISS索引
LOG_LEVEL=INFO          # 日志级别
```

### API配置

#### OpenAI API

```bash
# 官方OpenAI
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Azure OpenAI
OPENAI_API_KEY=your-azure-openai-key
OPENAI_BASE_URL=https://your-resource.openai.azure.com/
OPENAI_MODEL=gpt-35-turbo

# 第三方OpenAI兼容服务
OPENAI_API_KEY=your-third-party-key
OPENAI_BASE_URL=https://your-service.com/v1
OPENAI_MODEL=gpt-3.5-turbo
```

#### 阿里云通义千问

```bash
DASHSCOPE_API_KEY=sk-your-dashscope-api-key
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/api/v1
```

#### 自定义API服务

```bash
# 本地部署的模型服务
CUSTOM_API_KEY=your-custom-key
CUSTOM_BASE_URL=http://localhost:8000/v1
CUSTOM_MODEL=your-model-name
```

## 使用场景

### 场景1：完全本地部署

适合对数据隐私要求高的场景。

```bash
# 基础配置
USE_LOCAL_EMBEDDING=true
USE_FAISS=true
CHUNK_SIZE=1000
OVERLAP=100

# 不需要设置API密钥
# OPENAI_API_KEY=
# DASHSCOPE_API_KEY=
```

**优点**：
- 数据不离开本地
- 无API调用费用
- 无网络依赖

**缺点**：
- 需要下载模型文件
- 对硬件要求较高
- 无法使用摘要功能

### 场景2：使用OpenAI API

适合需要高质量摘要生成的场景。

```bash
# 基础配置
USE_LOCAL_EMBEDDING=false
CHUNK_SIZE=1000
OVERLAP=100

# OpenAI配置
OPENAI_API_KEY=sk-your-real-openai-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
```

**优点**：
- 高质量的摘要生成
- 无需本地GPU
- 快速响应

**缺点**：
- 需要API费用
- 数据会发送到OpenAI
- 需要网络连接

### 场景3：混合部署

本地嵌入 + 云端摘要，平衡性能和成本。

```bash
# 本地嵌入
USE_LOCAL_EMBEDDING=true
EMBEDDING_MODEL=Qwen/Qwen3-Embedding-4B

# 云端摘要
OPENAI_API_KEY=sk-your-openai-key
OPENAI_MODEL=gpt-3.5-turbo

# 或使用阿里云
DASHSCOPE_API_KEY=sk-your-dashscope-key
```

### 场景4：使用阿里云通义千问

适合中文内容处理。

```bash
# 基础配置
USE_LOCAL_EMBEDDING=false

# 阿里云配置
DASHSCOPE_API_KEY=sk-your-dashscope-key
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/api/v1
```

### 场景5：本地大模型服务

使用Ollama、vLLM等本地部署的模型。

```bash
# 基础配置
USE_LOCAL_EMBEDDING=true

# 本地模型服务（如Ollama）
CUSTOM_API_KEY=ollama
CUSTOM_BASE_URL=http://localhost:11434/v1
CUSTOM_MODEL=llama2

# 或vLLM服务
CUSTOM_API_KEY=your-vllm-token
CUSTOM_BASE_URL=http://localhost:8000/v1
CUSTOM_MODEL=Qwen/Qwen-7B-Chat
```

## 高级配置

### 性能调优

```bash
# 并发和内存
MAX_WORKERS=4           # 并发处理数
MEMORY_LIMIT_GB=8       # 内存限制
EMBEDDING_BATCH_SIZE=32 # 嵌入批处理大小

# API调用
API_TIMEOUT=60          # 超时时间
API_RETRY_TIMES=3       # 重试次数
API_RETRY_DELAY=1       # 重试延迟
```

### 文本处理

```bash
# 切分参数
MIN_CHUNK_SIZE=100      # 最小块大小
MAX_CHUNK_SIZE=4000     # 最大块大小

# 摘要参数
SUMMARY_MAX_LENGTH=300  # 摘要最大长度
SUMMARY_MIN_LENGTH=50   # 摘要最小长度
SUMMARY_TEMPERATURE=0.3 # 生成温度
SUMMARY_TOP_P=0.9       # Top-p采样
```

### 检索配置

```bash
DEFAULT_TOP_K=5         # 默认返回结果数
SIMILARITY_THRESHOLD=0.5 # 相似度阈值
EMBEDDING_DIMENSION=1024 # 向量维度
```

## 配置验证

### 检查配置是否正确加载

```python
from config import config, api_config

# 查看当前配置
print("当前配置:")
print(f"块大小: {config.DEFAULT_CHUNK_SIZE}")
print(f"使用本地嵌入: {config.USE_LOCAL_EMBEDDING}")
print(f"OpenAI API密钥: {api_config.openai_api_key[:10]}..." if api_config.openai_api_key else "未设置")
```

### 运行配置测试

```bash
python test_config.py
```

### 查看完整配置

```python
from config import config
import json

# 输出完整配置
config_dict = config.to_dict()
print(json.dumps(config_dict, indent=2, ensure_ascii=False))
```

## 环境变量优先级

配置加载的优先级顺序：

1. **系统环境变量**（最高优先级）
2. **`.env` 文件**
3. **代码中的默认值**（最低优先级）

这意味着你可以：
- 在 `.env` 文件中设置基础配置
- 在系统环境变量中覆盖特定设置
- 在生产环境中使用环境变量而不是文件

## 安全建议

### 1. 保护API密钥

```bash
# 确保.env文件不被提交到版本控制
echo ".env" >> .gitignore
echo ".env.local" >> .gitignore
echo ".env.*.local" >> .gitignore
```

### 2. 使用不同环境的配置

```bash
# 开发环境
.env.development

# 测试环境
.env.test

# 生产环境
.env.production
```

### 3. 定期轮换密钥

定期更新API密钥，特别是在以下情况：
- 密钥可能泄露
- 团队成员变动
- 定期安全审查

### 4. 最小权限原则

为API密钥设置最小必要的权限：
- OpenAI：只开启需要的模型访问权限
- 阿里云：使用RAM子账号，只授予必要的API权限

## 故障排除

### 常见问题

1. **配置未生效**
   - 检查环境变量名是否正确
   - 确认 `.env` 文件在正确位置
   - 重启应用程序

2. **API调用失败**
   - 验证API密钥是否正确
   - 检查网络连接
   - 确认API服务地址

3. **模型下载失败**
   - 检查网络连接
   - 配置代理（如需要）
   - 使用镜像源

### 调试方法

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查环境变量
import os
print("OPENAI_API_KEY:", os.getenv('OPENAI_API_KEY', '未设置'))
print("USE_LOCAL_EMBEDDING:", os.getenv('USE_LOCAL_EMBEDDING', '未设置'))
```

## 示例配置文件

查看 `.env.example` 文件获取完整的配置示例，包含所有可用的配置选项和详细说明。
